import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import numpy as np
import torch
import lasio

from src.models.networks import BiGRUNet
from src.pipelines.pseudolabel_training import train_with_pseudolabels
from src.rock_physics.mudrock import MudrockLine


def _get_curve(las, names):
    """
    Fetch a curve from a LAS object trying multiple fallback names.

    Parameters
    ----------
    las : lasio.LASFile
    names : list[str]
        Candidate curve mnemonics to try in order.
    """
    mnems = {c.mnemonic for c in las.curves}
    for n in names:
        if n in mnems:
            return np.asarray(las[n])
    # Not found
    return None


def _load_las_dataframe(las_path: Path):
    las = lasio.read(str(las_path))

    # Use specific curve names as requested
    vp = _get_curve(las, ["P-WAVE"])  # P-WAVE as input for vp
    vs = _get_curve(las, ["S-WAVE"])  # S-WAVE as target for vs
    gr = _get_curve(las, ["GR"])      # GR as input for gr
    den = _get_curve(las, ["RHOB"])   # RHOB as input for den
    res = _get_curve(las, ["RT"])     # RT as input for res

    # P-WAVE is already in velocity units (km/s or m/s), check if conversion needed
    if vp is not None:
        # If values are very large (>100), likely in m/s, convert to km/s
        if np.nanmedian(vp) > 100:
            vp = vp / 1000  # Convert m/s to km/s
            print(f"Converted P-WAVE from m/s to km/s")

    return {
        "vp": vp,
        "vs": vs,
        "gr": gr,
        "den": den,
        "res": res,
    }


def _prep_features(curves: dict[str, np.ndarray]):
    # Build feature matrix with available columns; must include vp for pseudolabels
    features = []
    names = []
    for key in ("vp", "gr", "den", "res"):
        if curves.get(key) is not None:
            arr = np.asarray(curves[key], dtype=float)
            if key == "res":
                arr = np.log(np.clip(arr, 1e-6, None))
            features.append(arr)
            names.append(key)
    if not features:
        raise RuntimeError("No usable features found in LAS")

    # Align by finite mask across all selected features (and y if available)
    X = np.vstack(features).T
    print(f"DEBUG: Feature matrix shape before masking: {X.shape}")
    print(f"DEBUG: Feature matrix NaN counts by column: {[np.sum(np.isnan(X[:, i])) for i in range(X.shape[1])]}")
    print(f"DEBUG: Feature matrix finite counts by column: {[np.sum(np.isfinite(X[:, i])) for i in range(X.shape[1])]}")
    
    mask = np.isfinite(X).all(axis=1)
    print(f"DEBUG: Mask - True count: {np.sum(mask)}, False count: {np.sum(~mask)}, Total: {len(mask)}")

    X = X[mask]
    print(f"DEBUG: Feature matrix shape after masking: {X.shape}")
    
    # Normalize (-1, 1)
    Xmin = X.min(axis=0)
    Xmax = X.max(axis=0)
    Xn = 2 * (X - Xmin) / (np.clip(Xmax - Xmin, 1e-9, None)) - 1

    return Xn.astype(np.float32), names, mask


def _prep_features_with_target(curves: dict[str, np.ndarray], y_all: np.ndarray = None):
    """Prepare features with target data included in finite mask calculation."""
    # Build feature matrix with available columns; must include vp for pseudolabels
    features = []
    names = []
    for key in ("vp", "gr", "den", "res"):
        if curves.get(key) is not None:
            arr = np.asarray(curves[key], dtype=float)
            if key == "res":
                arr = np.log(np.clip(arr, 1e-6, None))
            features.append(arr)
            names.append(key)
    if not features:
        raise RuntimeError("No usable features found in LAS")

    # Align by finite mask across all selected features AND target (if available)
    X = np.vstack(features).T
    print(f"DEBUG: Feature matrix shape before masking: {X.shape}")
    print(f"DEBUG: Feature matrix NaN counts by column: {[np.sum(np.isnan(X[:, i])) for i in range(X.shape[1])]}")
    print(f"DEBUG: Feature matrix finite counts by column: {[np.sum(np.isfinite(X[:, i])) for i in range(X.shape[1])]}")
    
    # Create mask that requires all features to be finite
    mask = np.isfinite(X).all(axis=1)
    
    # If target data is provided, also require target to be finite
    if y_all is not None:
        target_mask = np.isfinite(y_all)
        print(f"DEBUG: Target finite mask - True count: {np.sum(target_mask)}, False count: {np.sum(~target_mask)}")
        mask = mask & target_mask
        print(f"DEBUG: Combined mask (features + target) - True count: {np.sum(mask)}, False count: {np.sum(~mask)}, Total: {len(mask)}")
    else:
        print(f"DEBUG: Features-only mask - True count: {np.sum(mask)}, False count: {np.sum(~mask)}, Total: {len(mask)}")

    X = X[mask]
    print(f"DEBUG: Feature matrix shape after masking: {X.shape}")
    
    # Normalize (-1, 1)
    Xmin = X.min(axis=0)
    Xmax = X.max(axis=0)
    Xn = 2 * (X - Xmin) / (np.clip(Xmax - Xmin, 1e-9, None)) - 1

    return Xn.astype(np.float32), names, mask


def list_las_files(las_dir):
    """List all LAS files in the directory with details."""
    print(f"\nScanning LAS directory: {las_dir}")
    print("-" * 60)

    if not las_dir.exists():
        print(f"ERROR: LAS directory not found: {las_dir}")
        return []

    # Find all LAS files (case insensitive)
    las_files = []
    for pattern in ["*.las", "*.LAS"]:
        las_files.extend(las_dir.glob(pattern))

    las_files = sorted(set(las_files))  # Remove duplicates and sort

    if not las_files:
        print("No LAS files found in directory")
        return []

    print(f"Found {len(las_files)} LAS file(s):")
    for i, las_file in enumerate(las_files, 1):
        file_size = las_file.stat().st_size / 1024  # KB
        print(f"  {i:2d}. {las_file.name} ({file_size:.1f} KB)")

    return las_files


def analyze_las_file(las_path):
    """Analyze a single LAS file and show its structure."""
    print(f"\n" + "="*60)
    print(f"Analyzing LAS file: {las_path.name}")
    print("="*60)

    try:
        # Load LAS file using lasio
        las = lasio.read(str(las_path))

        # Show basic file info
        print(f"Well name: {las.well.WELL.value if las.well.WELL else 'Not specified'}")
        print(f"Company: {las.well.COMP.value if las.well.COMP else 'Not specified'}")
        print(f"Field: {las.well.FLD.value if las.well.FLD else 'Not specified'}")
        print(f"Location: {las.well.LOC.value if las.well.LOC else 'Not specified'}")

        # Show depth information
        if las.well.STRT and las.well.STOP:
            print(f"Depth range: {las.well.STRT.value} to {las.well.STOP.value} {las.well.STRT.unit}")

        # List all curves
        print(f"\nAvailable curves ({len(las.curves)}):")
        print("-" * 40)
        for curve in las.curves:
            unit = f" [{curve.unit}]" if curve.unit else ""
            desc = f" - {curve.descr}" if curve.descr else ""
            print(f"  {curve.mnemonic}{unit}{desc}")

        return las

    except Exception as e:
        print(f"ERROR loading LAS file: {e}")
        return None


def run_bigru_training_with_las():
    """Run BiGRU training with LAS data as a standalone script."""
    print("Starting BiGRU training with LAS data...")

    # Point to provided LAS directory
    las_dir = project_root / "Las"

    # List all LAS files in the directory
    las_files = list_las_files(las_dir)
    if not las_files:
        return False

    # Use the first LAS file found
    las_path = las_files[0]

    # Analyze the LAS file structure
    las = analyze_las_file(las_path)
    if las is None:
        return False

    print(f"\nProceeding with training using: {las_path.name}")
    curves = _load_las_dataframe(las_path)

    # Show which curves were found
    print("\nCurve availability:")
    curve_mapping = {
        "P-WAVE": "vp (input)",
        "S-WAVE": "vs (target)",
        "GR": "gr (input)",
        "RHOB": "den (input)",
        "RT": "res (input)"
    }

    for curve_name, description in curve_mapping.items():
        key = description.split()[0]
        status = "✓ Found" if curves[key] is not None else "✗ Missing"
        print(f"  {curve_name:8} ({description:12}): {status}")

    # Require P-WAVE (vp) to generate pseudolabel; proceed even if S-WAVE missing (unsupervised example)
    if curves["vp"] is None:
        print(f"\nERROR: P-WAVE curve is required but not found in {las_path.name}")
        return False

    print("Preparing features...")
    
    # Get raw target data first to include it in the masking process
    y_all = None
    if curves["vs"] is not None:
        print("Using actual S-WAVE measurements as target")
        y_all = np.asarray(curves["vs"], dtype=float)
        print(f"DEBUG: Original S-WAVE data - Shape: {y_all.shape}, NaN count: {np.sum(np.isnan(y_all))}, Finite count: {np.sum(np.isfinite(y_all))}")
        print(f"DEBUG: Min: {np.nanmin(y_all):.3f}, Max: {np.nanmax(y_all):.3f}, Mean: {np.nanmean(y_all):.3f}")
        
        # Convert S-WAVE from m/s to km/s to match P-WAVE units
        if np.nanmedian(y_all) > 100:
            y_all = y_all / 1000  # Convert m/s to km/s
            print(f"Converted S-WAVE from m/s to km/s")
            print(f"DEBUG: After conversion - Min: {np.nanmin(y_all):.3f}, Max: {np.nanmax(y_all):.3f}, Mean: {np.nanmean(y_all):.3f}")
    
    # Now get features and mask that includes target data
    Xn, feat_names, mask = _prep_features_with_target(curves, y_all)
    print(f"Features used: {feat_names}")
    print(f"Data points after cleaning: {len(Xn)}")

    # Physics pseudolabel from mudrock line needs vp aligned to the same mask
    vp = np.asarray(curves["vp"], dtype=float)
    vp = vp[mask]
    print("Generating physics-based pseudolabels...")
    phys = MudrockLine()
    vs_phys = phys.estimate_vs({"vp": vp}).astype(np.float32)

    # Apply mask to target data
    if y_all is not None:
        y = y_all[mask].astype(np.float32)
        print(f"DEBUG: After masking S-WAVE - Shape: {y.shape}, NaN count: {np.sum(np.isnan(y))}, Finite count: {np.sum(np.isfinite(y))}")
        if np.any(np.isfinite(y)):
            print(f"DEBUG: Finite values - Min: {np.nanmin(y):.3f}, Max: {np.nanmax(y):.3f}, Mean: {np.nanmean(y):.3f}")
    else:
        print("S-WAVE measurements not found, creating synthetic target for demonstration")
        # Create a synthetic target close to physics for demonstration
        rng = np.random.default_rng(0)
        y = (vs_phys + rng.normal(scale=0.05, size=vs_phys.shape)).astype(np.float32)

    # Model: add +1 feature for pseudolabel
    in_dim = Xn.shape[1] + 1
    model = BiGRUNet(in_dim=in_dim, hidden_dim=16, num_layers=1, use_tanh=True)
    print(f"Created BiGRU model with input dimension: {in_dim}")

    # Train briefly; this is a smoke test, not a benchmark
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"Training on device: {device}")
    print("Starting training...")

    model = train_with_pseudolabels(model, Xn, y, vs_phys, lr=1e-3, epochs=15, patience=5, device=device)
    print("Training completed!")

    # Predict on the same sequence
    print("Making predictions...")
    with torch.no_grad():
        X_aug = np.hstack([Xn, vs_phys[:, None]])
        xb = torch.from_numpy(X_aug).float().unsqueeze(0).to(device)
        preds = model(xb).detach().cpu().numpy().ravel()

    # Basic sanity checks
    if preds.shape != y.shape:
        print(f"ERROR: Prediction shape {preds.shape} doesn't match target shape {y.shape}")
        return False

    # Debug information for correlation issue
    print(f"\nDEBUG: Checking predictions and targets...")
    print(f"Predictions - Shape: {preds.shape}, Min: {np.min(preds):.6f}, Max: {np.max(preds):.6f}, Mean: {np.mean(preds):.6f}, Std: {np.std(preds):.6f}")
    print(f"Targets - Shape: {y.shape}, Min: {np.min(y):.6f}, Max: {np.max(y):.6f}, Mean: {np.mean(y):.6f}, Std: {np.std(y):.6f}")
    print(f"Predictions contains NaN: {np.any(np.isnan(preds))}, contains Inf: {np.any(np.isinf(preds))}")
    print(f"Targets contains NaN: {np.any(np.isnan(y))}, contains Inf: {np.any(np.isinf(y))}")
    print(f"Predictions variance: {np.var(preds):.6f}")
    print(f"Targets variance: {np.var(y):.6f}")
    
    # Check for constant values
    preds_unique = len(np.unique(preds))
    y_unique = len(np.unique(y))
    print(f"Predictions unique values: {preds_unique}, Targets unique values: {y_unique}")
    
    # If we synthesized y near physics, ensure predictions correlate with physics/target
    corr = np.corrcoef(preds, y)[0, 1]
    print(f"Raw correlation coefficient: {corr}")
    
    if not (np.isfinite(corr) and corr > 0.3):
        print(f"ERROR: Poor correlation between predictions and targets: {corr}")
        return False

    print(f"SUCCESS: Correlation between predictions and targets: {corr:.3f}")
    print(f"Prediction stats - Mean: {preds.mean():.3f}, Std: {preds.std():.3f}")
    print(f"Target stats - Mean: {y.mean():.3f}, Std: {y.std():.3f}")

    return True


def test_bigru_training_with_las():
    """Pytest-compatible test function."""
    result = run_bigru_training_with_las()
    assert result, "BiGRU training failed"


def list_las_files_only():
    """Just list LAS files and analyze their structure without training."""
    las_dir = project_root / "Las"

    # List all LAS files
    las_files = list_las_files(las_dir)
    if not las_files:
        return False

    # Analyze each LAS file
    for las_file in las_files:
        analyze_las_file(las_file)

    return True


if __name__ == "__main__":
    print("="*60)
    print("BiGRU LAS Training Example - Standalone Script")
    print("="*60)

    # Check command line arguments for mode selection
    if len(sys.argv) > 1 and sys.argv[1].lower() in ["list", "ls", "-l", "--list"]:
        print("Mode: List LAS files only\n")
        try:
            success = list_las_files_only()
            if success:
                print("\n" + "="*60)
                print("File listing completed!")
                print("="*60)
            else:
                print("\n" + "="*60)
                print("File listing failed")
                print("="*60)
                sys.exit(1)
        except Exception as e:
            print(f"\nUnexpected error: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)
    else:
        print("Mode: Full training pipeline\n")
        print("Usage: python script.py [list] - add 'list' to only show LAS files")
        print()
        try:
            success = run_bigru_training_with_las()
            if success:
                print("\n" + "="*60)
                print("Script completed successfully!")
                print("="*60)
            else:
                print("\n" + "="*60)
                print("Script failed - check error messages above")
                print("="*60)
                sys.exit(1)
        except Exception as e:
            print(f"\nUnexpected error: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)