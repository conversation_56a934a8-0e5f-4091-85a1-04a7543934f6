import numpy as np
import pandas as pd
import torch

from src.data.loaders import load_logs_csv
from src.rock_physics.mudrock import MudrockLine
from src.models.networks import BiGRUNet
from src.pipelines.pseudolabel_training import train_with_pseudolabels


def test_pseudolabel_training(tmp_path):
    # Create synthetic dataset
    n = 20
    rng = np.random.default_rng(0)
    vp = np.linspace(2.0, 3.5, n)
    vs_true = (vp - 1.36) / 1.16 + rng.normal(scale=0.05, size=n)
    gr = rng.uniform(0, 100, size=n)
    den = rng.uniform(2.0, 2.6, size=n)
    res = rng.uniform(1, 100, size=n)
    df = pd.DataFrame({"vp": vp, "gr": gr, "den": den, "res": res, "vs": vs_true})
    path = tmp_path / "synthetic.csv"
    df.to_csv(path, index=False)

    feature_cols = ["vp", "gr", "den", "res"]
    Xn, y = load_logs_csv(path, feature_cols, "vs")

    phys = MudrockLine()
    vs_phys = phys.estimate_vs({"vp": df["vp"].to_numpy()})

    model = BiGRUNet(in_dim=Xn.shape[1] + 1, hidden_dim=8)
    trained = train_with_pseudolabels(model, Xn, y, vs_phys, epochs=20, patience=5)

    with torch.no_grad():
        xb = torch.from_numpy(np.hstack([Xn, vs_phys[:, None]])).float().unsqueeze(0)
        preds = trained(xb).cpu().numpy().ravel()

    rmse = np.sqrt(np.mean((preds - y) ** 2))
    assert rmse < 0.2
