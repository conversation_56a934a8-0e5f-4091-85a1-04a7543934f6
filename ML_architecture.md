# Physics-Guided BiGRU ML Architecture

This document provides a self-contained, detailed description of the machine learning architecture used to predict shear sonic velocity (VS) from well logs using a physics-guided bidirectional GRU (BiGRU). It explains the underlying rock-physics theory, the full data and modeling pipeline, design rationales, mathematical formulations, and practical trade-offs. For traceability to the codebase, a few pointers to modules are included, but everything needed to understand the approach is written here.

Helpful code pointers:
- `bigru_training_with_outputs.py` — orchestration, training loop, predictions, plots, and reports
- `src/utils/las_processing.py` — LAS ingestion, masking, preprocessing, normalization
- `src/rock_physics/mudrock.py` — Mudrock line physics model (VP→VS)
- `src/models/networks.py` — BiGRU definition
- `src/pipelines/pseudolabel_training.py` — pseudolabel-based training routine
- `src/models/losses.py` — physics-guided loss (optional strategy)
- `configs/default.yaml` — default features and target mapping


## Fundamental Theory: Why Physics-Guided?

Purely data-driven models may overfit or extrapolate poorly, especially when labels (shear sonic VS logs) are sparse or noisy. Physics-guided learning injects domain knowledge to regularize the solution and steer the model toward geologically plausible predictions. For clastic rocks (sand–shale mixtures), a simple but powerful empirical constraint is the Mudrock line (Castagna et al., 1985), used effectively in physics-guided ML studies such as Zhao et al. (2024).

Mudrock line (velocities in km/s):
- VP ≈ 1.16 · VS + 1.36
- Rearranged to estimate VS from VP:  VS_hat_mudrock = (VP − 1.36) / 1.16

We use the Mudrock estimate as a physics pseudolabel (an additional model input feature) to guide the BiGRU. This first-order prior helps the network learn plausible VS trends, especially where true VS is missing or noisy. Crucially, the model is not forced to match physics; it is allowed to deviate when data justify doing so.

Scope and caveats:
- The Mudrock line is a first-order approximation for clastic rocks; departures are expected in carbonates, unconsolidated sediments, or unusual lithologies/fluids.
- The relationship assumes consistent units (km/s). Unit mistakes can dominate errors.
- Local calibration (re-fitting slope/intercept) can adapt the line to specific basins or wells when adequate VS labels are available.

References (core concepts):
- Castagna, J.P., Batzle, M.L., and Eastwood, R.L. (1985). Relationships between compressional-wave and shear-wave velocities in clastic silicate rocks. Geophysics, 50(4).
- Zhao et al. (2024). Rock-physics-guided machine learning for shear sonic log prediction, Geophysics 89(1).


## End-to-End Data Flow

High-level flow from raw LAS to plots and reports:

- LAS files `Las/*.las` → `src/utils/las_processing.py`
  - Extract curves: VP, VS (if available), GR, RHOB, RT, DEPTH
  - Unit checks: convert VP/VS from m/s to km/s when needed
  - Preprocessing: natural log transform for resistivity (RT), mask NaNs, normalize features to (−1, 1)
- Physics model `src/rock_physics/mudrock.py`
  - Compute VS_hat_mudrock from VP in km/s
- Training data assembly `bigru_training_with_outputs.py`
  - Build Xn (normalized features), y (VS), vs_phys (physics estimate), depth mask
  - Augment features with physics estimate: X_aug = [Xn, vs_phys]
- Sequence model `src/models/networks.py` (BiGRU)
  - Input shape [B, T, F]; here B=1 (single sequence), T≈depth samples, F=(features + 1 physics feature)
- Training `src/pipelines/pseudolabel_training.py`
  - Loss: MSE to true VS, early stopping and gradient clipping
- Inference and outputs `bigru_training_with_outputs.py`
  - Predictions on training and test wells
  - Metrics: Corr, RMSE, MAE, R²
  - Visuals: scatter plots, depth tracks, residual histograms, error analysis
  - Artifacts: model weights, CSVs, PNGs, and summary report

If your Markdown viewer supports Mermaid, this diagram summarizes the architecture:

```mermaid
flowchart LR
    A[LAS files] --> B[LAS processing\n- unit checks\n- log transform\n- masking\n- normalization]
    B --> C[Features Xn, Target y, Depth]
    C --> D[Mudrock VS_hat = (VP-1.36)/1.16]
    D --> E[X_aug = [Xn, VS_hat]]
    E --> F[BiGRU model]
    F --> G[Predictions y_pred]
    G --> H[Metrics Corr/RMSE/MAE/R²]
    G --> I[Plots: scatter/depth/residuals]
    G --> J[Reports + CSVs]
```


## Data Ingestion and Preprocessing

Data schema expected from LAS:
- VP (P‑wave), VS (S‑wave, optional label), GR (gamma ray), RHOB (bulk density), RT (resistivity), DEPTH

Key steps and reasoning:
- Curve extraction and aliasing: we fetch by canonical mnemonics (VP: `P-WAVE`, VS: `S-WAVE`, GR: `GR`, RHOB: `RHOB`, RT: `RT`, DEPTH: `DEPTH`). If your LAS uses different mnemonics, adapt the alias list accordingly.
- Unit consistency: velocities must be in km/s. A simple heuristic converts m/s→km/s when the median > 100. Resistivity units are handled via a log transform (below).
- Resistivity transform: resistivity is heavy-tailed; we apply a natural logarithm to stabilize scale and reduce skew: `res = log(max(res, 1e-6))`. Using log10 is equally valid if applied consistently during training and inference.
- Finite masking: a sample is kept only if all selected features are finite (and the target if supervised training is used). This avoids leaking NaNs/Infs into the model and silently propagating errors.
- Normalization to (−1, 1): per-feature min–max scaling improves optimization stability and keeps the physics feature on a comparable scale to the logs. For feature j with min a_j and max b_j, the normalized value is `x'_j = 2*(x_j − a_j)/max(b_j − a_j, ε) − 1`, with ε≈1e‑9 to avoid division by zero.
- Depth track: used for plotting and CSVs; if missing, a simple 0..N index is used.

Outputs of preprocessing:
- `Xn ∈ R^{N×p}` normalized features (default p=4: VP, GR, DEN, RES)
- `y ∈ R^{N}` true VS when available
- `mask ∈ {0,1}^M` indexing the rows kept after masking
- `depth ∈ R^{N}` for visualization/exports


## Physics Model (Mudrock)

We implement the Mudrock relation as a small, easily swappable component that takes VP and returns a physics-based VS estimate:

- Equation: `VS_hat = (VP − 1.36) / 1.16` (km/s)
- Usage: VS_hat is appended to the model inputs as an additional feature (pseudolabel). The model remains free to deviate from this estimate based on other logs and learned context.

Optional local calibration: When a training well has reliable VS labels, one can fit VP ≈ s·VS + c via least squares on that well to obtain slope/intercept (s, c), then derive `VS_hat = (VP − c)/s`. This adapts the physics prior to local geology.

Benefits of the modular design:
- Easy to replace with richer rock-physics models (empirical VP–VS, multiparameter regression, Xu‑White/SCA) without touching the ML code.
- Enables side-by-side comparisons of different physics priors.


## Model: BiGRU for Depth Sequences

Architecture summary:
- Input: a depth sequence with features per depth sample. We set batch size B=1 and feed the full well as a single sequence of length T (number of valid samples). Feature dimension F = p + 1 (logs plus VS_hat physics feature).
- Core: a bidirectional GRU layer processes the sequence forward and backward, concatenating hidden states to capture context from both shallower and deeper samples.
- Nonlinearity: Tanh after the GRU stabilizes activations and matches prior work; ReLU can be used but may yield sharper transitions.
- Head: a linear layer maps the concatenated hidden state at each depth to a scalar VS prediction.

Shapes:
- Input tensor: `x ∈ R^{B×T×F}`
- GRU output: `h ∈ R^{B×T×(2H)}` where H is hidden_dim
- Prediction: `y_pred ∈ R^{B×T}` after the linear head and squeeze

Why BiGRU?
- Vertical context matters: lithological trends and compaction vary smoothly with depth. A bidirectional GRU allows each prediction to consider neighboring samples above and below, often reducing noise compared to pointwise regressors.
- Efficient and stable: with modest H (e.g., 16) and a single layer, BiGRUs train quickly and are robust for well-log length sequences.


## Training Strategy: Physics Pseudolabels (Default)

Procedure:
- Feature augmentation: compute `VS_hat` from VP and append as an extra feature: `X_aug = concat(Xn, VS_hat) ∈ R^{N×(p+1)}`.
- Sequence packing: reshape to `xb ∈ R^{1×T×F}` with T=N, F=p+1.
- Objective: minimize mean squared error (MSE) to the true `VS` labels: `L = (1/T)∑(y_pred − y_true)^2`.
- Optimization: Adam, lr≈1e‑3; gradient clipping (e.g., 1.0) prevents exploding gradients.
- Early stopping: track best validation/train loss; stop when no improvement exceeds tolerance for `patience` epochs, with a minimum epoch floor to avoid premature stops.

Rationale for pseudolabeling:
- Injects domain knowledge without enforcing a hard constraint. When the physics is approximately correct, it provides a strong prior; when local geology departs, the model can learn to override it using other logs and sequence context.

Implementation notes:
- Training on full sequences (B=1) preserves context; for very long wells or many wells, a sliding-window approach can increase stochasticity and potentially generalization.
- The physics feature should be on a comparable numeric scale to other inputs; using the same normalization regime helps.


## Alternative Physics-Guided Strategies (Available/Pluggable)

1) Physics-guided loss (adaptive "min" formulation):

Let `y_true` be VS labels and `y_phys` be the physics estimate (e.g., Mudrock). Define

```
L_data = MSE(y_pred, y_true)
L_phys = MSE(y_pred, y_phys)
L_total = L_data + min(L_data, L_phys)
```

Interpretation: we always minimize data error, and we add a physics term only up to the point where it does not dominate data fidelity. This stabilizes training when physics is broadly correct but not exact and avoids over-penalizing justified deviations.

2) Transfer learning (physics pretraining → supervised fine-tuning):

- Stage A (pretrain): train the network to predict `y_phys` from logs (no true `y` required). This imprints physics-consistent structure in the weights.
- Stage B (fine-tune): continue training on true `y_true` with a lower learning rate (e.g., 10× smaller). Optionally reduce patience or apply early stopping to prevent overfitting.

When to use:
- Pseudolabels: default, simplest to integrate, works well when physics is helpful and labels exist for at least one training well.
- Physics-guided loss: use when you want an explicit, soft regularization toward physics across the training trajectory.
- Transfer learning: useful when labeled VS is very scarce; physics can bootstrap the model prior to limited supervised fine-tuning.


## Metrics, Plots, and Artifacts

Computed metrics (training and tests):
- Pearson correlation: `corr = cov(y_pred, y_true) / (σ_pred σ_true)`
- RMSE (km/s): `RMSE = sqrt((1/T)∑(y_pred − y_true)^2)`
- MAE (km/s): `MAE = (1/T)∑|y_pred − y_true|`
- Coefficient of determination: `R² = 1 − SSE/SST`, where `SSE = ∑(y_true − y_pred)^2` and `SST = ∑(y_true − ȳ_true)^2` (guarded against `SST=0`).

Visualizations (saved as PNG under `output/bigru_results_YYYYMMDD_HHMMSS/`):
- `bigru_results_overview_*.png` —
  - Scatter: Predicted vs Actual with 1:1 line, annotated Corr/R²/MAE/RMSE
  - Scatter: Physics vs Actual, annotated Corr/R²/MAE/RMSE
  - Depth tracks: Actual, Predicted, Physics with MAE/RMSE summaries
  - Residual histograms: BiGRU vs Physics with MAE/RMSE summaries
- `error_analysis_*.png` — boxplots with mean/median annotations for absolute and relative errors

Tabular and report outputs:
- `predictions_vs_actual.csv` — depth-aligned predictions, physics estimates, and residuals
- `summary_report.txt` — configuration, data stats, and performance summary
- `training_config.json`, `training_results.json`, `bigru_model.pth`


## Configuration

Module: `configs/default.yaml`
- `feature_cols: [vp, gr, den, res]`
- `target_col: vs`

Runtime training knobs are defined in `bigru_training_with_outputs.py` within `training_config` (e.g., hidden size, learning rate, epochs, patience, device). Adjust here to tune capacity and convergence.


## Dimensions and Shapes (at a glance)

- Raw features: X (N, 4) from VP, GR, DEN, RES
- Normalized features: Xn (N, 4)
- Physics pseudolabel: vs_phys (N,)
- Augmented input: X_aug (N, 5)
- Batched for sequence model: xb (1, T=N, F=5)
- Model output: y_pred (1, T) → flatten to (N,)


## Assumptions and Limitations

- Units: velocities in km/s; auto-convert from m/s when detected.
- Resistivity transform: current implementation uses natural log; confirm with your petrophysics standard before changing to log10.
- Normalization: min–max to (−1, 1) fit on the training data used in each run.
- Batch size: sequences are trained and inferred with B=1 (full well). For large datasets, consider windowed training to diversify gradients.
- Physics model: Mudrock line is a first-order approximation; richer rock-physics can be added with the same interface.

Additional considerations:
- Outliers and tool issues: consider capping extreme values or applying robust scaling if your basin shows frequent spikes (e.g., washouts affecting density).
- Data leakage: fit normalization on training wells only when doing blind-well evaluation; then apply the same scaler to test wells.
- Depth sampling: if wells have different sampling intervals, resample to a common grid or handle variable T per well by batching one well at a time.


## Extending the Architecture

- Add new physics constraints by implementing `estimate_vs(logs)` in a new class under `src/rock_physics/`.
- Swap training strategies by adding pipelines under `src/pipelines/` (physics-guided loss, transfer learning).
- Add QC plots or different metrics in `bigru_training_with_outputs.py`.
- Promote config-driven experiments by expanding `configs/default.yaml` (e.g., model size, strategy selection, transforms).


## Design Rationale and Trade-offs

- Physics as a feature vs physics in the loss: appending the physics estimate is simple and flexible. A loss term makes the preference explicit but must be balanced to avoid suppressing legitimate deviations from physics.
- BiGRU vs 1D CNN/Transformer: BiGRU is light-weight, captures bidirectional context, and works well with modest data. CNNs can be faster but less adaptive to variable sequence length; transformers need more data and careful regularization.
- Full-sequence vs sliding windows: full-sequence preserves long-range trends; windows increase stochasticity and can help generalization across wells at the cost of some context.
- Tanh vs ReLU: Tanh can yield smoother outputs suited to logs; ReLU may better preserve sharp contrasts (e.g., at formation boundaries) but can produce piecewise-linear artifacts.


## Failure Modes and QA Checks

- Unit mismatch (m/s vs km/s) — verify automatic conversion via median thresholds and spot-check plots.
- Missing curves — ensure masking statistics are reasonable; extremely low keep-rates indicate a curve quality/aliasing issue.
- Overfitting — watch for high training correlation but poor blind-well performance; consider patience, windowing, or regularization.
- Drift vs depth — compare predictions to physics and actuals on depth tracks; large systematic drift suggests normalization or feature issues.


## Reproducibility Tips

- Fix random seeds for NumPy/PyTorch where practical.
- Log all hyperparameters and feature lists (the script snapshots these to JSON).
- Keep a record of LAS preprocessing decisions (log base, unit conversions).


## Quick Pointers to Code Symbols

- Physics: `MudrockLine.estimate_vs()` in `src/rock_physics/mudrock.py`
- Preprocessing: `prep_features_with_target()` and `convert_swave_units()` in `src/utils/las_processing.py`
- Model: `BiGRUNet` in `src/models/networks.py`
- Training (current): `train_with_pseudolabels()` in `src/pipelines/pseudolabel_training.py`
- Loss (optional): `physics_guided_loss()` in `src/models/losses.py`
- Orchestrator: `run_bigru_training_with_outputs()` in `bigru_training_with_outputs.py`


## References

- Zhao et al. (2024). Physics-guided ML for shear sonic prediction, Geophysics 89(1).
- Castagna et al. (1985). Relationships between compressional-wave and shear-wave velocities in clastic silicate rocks, Geophysics 50(4).
  (Additional implementation details are embedded in the code modules referenced above.)
