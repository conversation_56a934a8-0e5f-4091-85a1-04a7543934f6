"""
LAS file processing utilities for BiGRU training.

This module contains shared functions for loading and processing LAS files,
eliminating redundancy between training scripts and test files.
"""

import numpy as np
import lasio
from pathlib import Path
from typing import Dict, List, Tuple, Optional


def get_curve(las: lasio.LASFile, names: List[str]) -> Optional[np.ndarray]:
    """
    Fetch a curve from a LAS object trying multiple fallback names.

    Parameters
    ----------
    las : lasio.LASFile
        The LAS file object
    names : list[str]
        Candidate curve mnemonics to try in order.

    Returns
    -------
    np.ndarray or None
        The curve data if found, None otherwise
    """
    mnems = {c.mnemonic for c in las.curves}
    for n in names:
        if n in mnems:
            return np.asarray(las[n])
    return None


def load_las_dataframe(las_path: Path) -> Dict[str, Optional[np.ndarray]]:
    """
    Load LAS file and extract standard curves.

    Parameters
    ----------
    las_path : Path
        Path to the LAS file

    Returns
    -------
    dict
        Dictionary containing curve data with keys: vp, vs, gr, den, res, depth
    """
    las = lasio.read(str(las_path))

    # Use specific curve names as requested
    vp = get_curve(las, ["P-WAVE"])  # P-WAVE as input for vp
    vs = get_curve(las, ["S-WAVE"])  # S-WAVE as target for vs
    gr = get_curve(las, ["GR"])      # GR as input for gr
    den = get_curve(las, ["RHOB"])   # RHOB as input for den
    res = get_curve(las, ["RT"])     # RT as input for res
    depth = get_curve(las, ["DEPTH"])  # Depth for reference

    # P-WAVE is already in velocity units (km/s or m/s), check if conversion needed
    if vp is not None:
        # If values are very large (>100), likely in m/s, convert to km/s
        if np.nanmedian(vp) > 100:
            vp = vp / 1000  # Convert m/s to km/s
            print(f"Converted P-WAVE from m/s to km/s")

    return {
        "vp": vp,
        "vs": vs,
        "gr": gr,
        "den": den,
        "res": res,
        "depth": depth,
    }


def prep_features_with_target(curves: Dict[str, np.ndarray], 
                             y_all: Optional[np.ndarray] = None) -> Tuple[np.ndarray, List[str], np.ndarray]:
    """
    Prepare features with target data included in finite mask calculation.

    Parameters
    ----------
    curves : dict
        Dictionary of curve data
    y_all : np.ndarray, optional
        Target data to include in masking

    Returns
    -------
    tuple
        (normalized_features, feature_names, mask)
    """
    # Build feature matrix with available columns; must include vp for pseudolabels
    features = []
    names = []
    for key in ("vp", "gr", "den", "res"):
        if curves.get(key) is not None:
            arr = np.asarray(curves[key], dtype=float)
            if key == "res":
                arr = np.log(np.clip(arr, 1e-6, None))
            features.append(arr)
            names.append(key)
    
    if not features:
        raise RuntimeError("No usable features found in LAS")

    # Align by finite mask across all selected features AND target (if available)
    X = np.vstack(features).T
    print(f"DEBUG: Feature matrix shape before masking: {X.shape}")
    print(f"DEBUG: Feature matrix NaN counts by column: {[np.sum(np.isnan(X[:, i])) for i in range(X.shape[1])]}")
    print(f"DEBUG: Feature matrix finite counts by column: {[np.sum(np.isfinite(X[:, i])) for i in range(X.shape[1])]}")
    
    # Create mask that requires all features to be finite
    mask = np.isfinite(X).all(axis=1)
    
    # If target data is provided, also require target to be finite
    if y_all is not None:
        target_mask = np.isfinite(y_all)
        print(f"DEBUG: Target finite mask - True count: {np.sum(target_mask)}, False count: {np.sum(~target_mask)}")
        mask = mask & target_mask
        print(f"DEBUG: Combined mask (features + target) - True count: {np.sum(mask)}, False count: {np.sum(~mask)}, Total: {len(mask)}")
    else:
        print(f"DEBUG: Features-only mask - True count: {np.sum(mask)}, False count: {np.sum(~mask)}, Total: {len(mask)}")

    X = X[mask]
    print(f"DEBUG: Feature matrix shape after masking: {X.shape}")
    
    # Normalize (-1, 1)
    Xmin = X.min(axis=0)
    Xmax = X.max(axis=0)
    Xn = 2 * (X - Xmin) / (np.clip(Xmax - Xmin, 1e-9, None)) - 1

    return Xn.astype(np.float32), names, mask


def list_las_files(las_dir: Path) -> List[Path]:
    """
    List all LAS files in the directory with details.

    Parameters
    ----------
    las_dir : Path
        Directory to search for LAS files

    Returns
    -------
    list[Path]
        List of LAS file paths found
    """
    print(f"\nScanning LAS directory: {las_dir}")
    print("-" * 60)

    if not las_dir.exists():
        print(f"ERROR: LAS directory not found: {las_dir}")
        return []

    # Find all LAS files (case insensitive)
    las_files = []
    for pattern in ["*.las", "*.LAS"]:
        las_files.extend(las_dir.glob(pattern))

    las_files = sorted(set(las_files))  # Remove duplicates and sort

    if not las_files:
        print("No LAS files found in directory")
        return []

    print(f"Found {len(las_files)} LAS file(s):")
    for i, las_file in enumerate(las_files, 1):
        file_size = las_file.stat().st_size / 1024  # KB
        print(f"  {i:2d}. {las_file.name} ({file_size:.1f} KB)")

    return las_files


def analyze_las_file(las_path: Path) -> Optional[lasio.LASFile]:
    """
    Analyze a single LAS file and show its structure.

    Parameters
    ----------
    las_path : Path
        Path to the LAS file

    Returns
    -------
    lasio.LASFile or None
        The loaded LAS file object, or None if loading failed
    """
    print(f"\n" + "="*60)
    print(f"Analyzing LAS file: {las_path.name}")
    print("="*60)

    try:
        # Load LAS file using lasio
        las = lasio.read(str(las_path))

        # Show basic file info
        print(f"Well name: {las.well.WELL.value if las.well.WELL else 'Not specified'}")
        print(f"Company: {las.well.COMP.value if las.well.COMP else 'Not specified'}")
        print(f"Field: {las.well.FLD.value if las.well.FLD else 'Not specified'}")
        print(f"Location: {las.well.LOC.value if las.well.LOC else 'Not specified'}")

        # Show depth information
        if las.well.STRT and las.well.STOP:
            print(f"Depth range: {las.well.STRT.value} to {las.well.STOP.value} {las.well.STRT.unit}")

        # List all curves
        print(f"\nAvailable curves ({len(las.curves)}):")
        print("-" * 40)
        for curve in las.curves:
            unit = f" [{curve.unit}]" if curve.unit else ""
            desc = f" - {curve.descr}" if curve.descr else ""
            print(f"  {curve.mnemonic}{unit}{desc}")

        return las

    except Exception as e:
        print(f"ERROR loading LAS file: {e}")
        return None


def convert_swave_units(y_all: np.ndarray) -> np.ndarray:
    """
    Convert S-wave data from m/s to km/s if needed.

    Parameters
    ----------
    y_all : np.ndarray
        S-wave velocity data

    Returns
    -------
    np.ndarray
        S-wave data in km/s units
    """
    if np.nanmedian(y_all) > 100:
        y_all = y_all / 1000  # Convert m/s to km/s
        print(f"Converted S-WAVE from m/s to km/s")
    return y_all