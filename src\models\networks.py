"""Neural network architectures."""
from __future__ import annotations

import torch
from torch import nn


class BiGRUNet(nn.Module):
    """Simple bidirectional GRU network with a linear head."""

    def __init__(self, in_dim: int, hidden_dim: int = 16, num_layers: int = 1, use_tanh: bool = True) -> None:
        super().__init__()
        self.gru = nn.GRU(in_dim, hidden_dim, num_layers=num_layers, batch_first=True, bidirectional=True)
        self.act = nn.Tanh() if use_tanh else nn.ReLU()
        self.fc = nn.Linear(2 * hidden_dim, 1)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass.

        Parameters
        ----------
        x : torch.Tensor
            Tensor of shape (batch, seq, features).
        """
        out, _ = self.gru(x)
        out = self.act(out)
        out = self.fc(out)
        return out.squeeze(-1)
