"""Loss functions for training."""
from __future__ import annotations

import torch
from torch import nn


def physics_guided_loss(y_pred: torch.Tensor, y_true: torch.Tensor, y_phys: torch.Tensor, base: str = "mse") -> torch.Tensor:
    """Adaptive physics-guided loss from <PERSON> et al. (2024).

    Parameters
    ----------
    y_pred, y_true, y_phys : torch.Tensor
        Predicted, true and physics-based S-wave velocities.
    base : str
        Base loss type, currently only mean-squared error (``"mse"``) is
        implemented.
    """
    if base != "mse":
        raise ValueError("Only MSE base loss is implemented")
    mse = nn.MSELoss()
    lossa = mse(y_pred, y_true)
    lossb = mse(y_pred, y_phys)
    return lossa + torch.min(lossa, lossb)
