"""
Test module for LAS processing utilities.

This module contains focused tests for the shared LAS processing functions.
"""

import os
import sys
from pathlib import Path
import pytest
import numpy as np

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.las_processing import (
    list_las_files,
    analyze_las_file,
    load_las_dataframe,
    prep_features_with_target,
    convert_swave_units
)


def test_list_las_files():
    """Test LAS file discovery functionality."""
    las_dir = project_root / "Las"
    
    if not las_dir.exists():
        pytest.skip("LAS directory not found")
    
    las_files = list_las_files(las_dir)
    
    # Should find at least one LAS file
    assert len(las_files) > 0, "No LAS files found in directory"
    
    # All returned paths should be LAS files
    for las_file in las_files:
        assert las_file.suffix.lower() == '.las', f"Non-LAS file found: {las_file}"
        assert las_file.exists(), f"LAS file does not exist: {las_file}"


def test_load_las_dataframe():
    """Test LAS file loading and curve extraction."""
    las_dir = project_root / "Las"
    
    if not las_dir.exists():
        pytest.skip("LAS directory not found")
    
    las_files = list_las_files(las_dir)
    if not las_files:
        pytest.skip("No LAS files found")
    
    # Test with first available LAS file
    las_path = las_files[0]
    curves = load_las_dataframe(las_path)
    
    # Should return a dictionary with expected keys
    expected_keys = {"vp", "vs", "gr", "den", "res", "depth"}
    assert set(curves.keys()) == expected_keys
    
    # At least vp should be available for training
    assert curves["vp"] is not None, "P-WAVE curve is required but not found"


def test_prep_features_with_target():
    """Test feature preparation functionality."""
    las_dir = project_root / "Las"
    
    if not las_dir.exists():
        pytest.skip("LAS directory not found")
    
    las_files = list_las_files(las_dir)
    if not las_files:
        pytest.skip("No LAS files found")
    
    las_path = las_files[0]
    curves = load_las_dataframe(las_path)
    
    if curves["vp"] is None:
        pytest.skip("P-WAVE curve not available in test file")
    
    # Test without target
    Xn, feat_names, mask = prep_features_with_target(curves)
    
    assert isinstance(Xn, np.ndarray), "Features should be numpy array"
    assert Xn.dtype == np.float32, "Features should be float32"
    assert len(feat_names) > 0, "Should have at least one feature"
    assert "vp" in feat_names, "vp should be in features"
    assert isinstance(mask, np.ndarray), "Mask should be numpy array"
    assert mask.dtype == bool, "Mask should be boolean"
    
    # Test with target if available
    if curves["vs"] is not None:
        y_all = np.asarray(curves["vs"], dtype=float)
        y_all = convert_swave_units(y_all)
        
        Xn_with_target, feat_names_with_target, mask_with_target = prep_features_with_target(curves, y_all)
        
        assert Xn_with_target.shape[1] == len(feat_names_with_target)
        assert np.sum(mask_with_target) <= np.sum(mask), "Target mask should be more restrictive"


def test_convert_swave_units():
    """Test S-wave unit conversion."""
    # Test data in m/s (should be converted)
    swave_ms = np.array([2000.0, 2500.0, 3000.0])  # m/s
    converted = convert_swave_units(swave_ms)
    expected = swave_ms / 1000  # km/s
    np.testing.assert_array_equal(converted, expected)
    
    # Test data already in km/s (should not be converted)
    swave_kms = np.array([2.0, 2.5, 3.0])  # km/s
    not_converted = convert_swave_units(swave_kms)
    np.testing.assert_array_equal(not_converted, swave_kms)


def test_analyze_las_file():
    """Test LAS file analysis functionality."""
    las_dir = project_root / "Las"
    
    if not las_dir.exists():
        pytest.skip("LAS directory not found")
    
    las_files = list_las_files(las_dir)
    if not las_files:
        pytest.skip("No LAS files found")
    
    las_path = las_files[0]
    las_obj = analyze_las_file(las_path)
    
    # Should return a LAS object or None
    assert las_obj is not None, "LAS file should be loadable"
    
    # Should have curves
    assert len(las_obj.curves) > 0, "LAS file should have curves"


if __name__ == "__main__":
    # Run tests when executed directly
    pytest.main([__file__, "-v"])