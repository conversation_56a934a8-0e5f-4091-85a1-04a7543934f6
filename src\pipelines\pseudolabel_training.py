"""Training pipeline using physics-guided pseudolabels."""
from __future__ import annotations

from typing import Optional

import numpy as np
import torch
from torch import nn

from src.models.networks import BiGRUNet


def train_with_pseudolabels(
    model: BiGRUNet,
    X_train: np.ndarray,
    y_train: np.n<PERSON><PERSON>,
    vs_phys: np.n<PERSON><PERSON>,
    lr: float = 1e-3,
    epochs: int = 50,
    patience: int = 5,
    device: str | torch.device = "cpu",
    steps_per_epoch: int = 10,
    min_epochs: int = 10,
    verbose: bool = True,
    log_every: int = 1,
) -> BiGRUNet:
    """Train a model with physics-guided pseudolabel feature.

    Parameters
    ----------
    model : BiGRUNet
        The network to train.
    X_train : np.ndarray
        Normalized feature matrix of shape (n_samples, n_features).
    y_train : np.ndarray
        True S-wave velocities of shape (n_samples,).
    vs_phys : np.ndarray
        Physics-based estimates appended as a feature.
    lr, epochs, patience :
        Optimizer configuration.
    steps_per_epoch : int
        Number of optimization steps to run per epoch (helps small datasets converge
        faster without changing the public API used by tests).
    device : str or torch.device
        Device to run training on.
    verbose : bool
        If True, prints epoch-by-epoch progress with loss and early stopping status.
    log_every : int
        Print frequency in epochs when verbose is True.
    """
    model = model.to(device)
    X_aug = np.hstack([X_train, vs_phys[:, None]])
    xb = torch.from_numpy(X_aug).float().unsqueeze(0).to(device)
    yb = torch.from_numpy(y_train).float().unsqueeze(0).to(device)

    opt = torch.optim.Adam(model.parameters(), lr=lr)
    criterion = nn.MSELoss()

    best_loss: float = float("inf")
    best_state: Optional[dict] = None
    patience_ctr = 0

    for epoch in range(epochs):
        model.train()
        cur_loss = None
        for _step in range(max(1, int(steps_per_epoch))):
            opt.zero_grad()
            out = model(xb)
            loss = criterion(out, yb)
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            opt.step()
            cur_loss = loss.item()

        if cur_loss + 1e-6 < best_loss:
            best_loss = cur_loss
            best_state = {k: v.detach().clone() for k, v in model.state_dict().items()}
            patience_ctr = 0
        else:
            patience_ctr += 1
            # Enforce a minimum number of epochs before early stopping
            if patience_ctr >= patience and epoch + 1 >= min_epochs:
                if verbose:
                    print(f"Early stopping at epoch {epoch+1}/{epochs} | best_loss={best_loss:.6f}")
                break

        if verbose and (epoch % max(1, int(log_every)) == 0 or epoch + 1 == epochs):
            print(
                f"Epoch {epoch+1:3d}/{epochs} | loss={cur_loss:.6f} | best={best_loss:.6f} | patience={patience_ctr}/{patience}"
            )

    if best_state is not None:
        model.load_state_dict(best_state)
        if verbose:
            print("Restored best model weights from training.")
    return model
