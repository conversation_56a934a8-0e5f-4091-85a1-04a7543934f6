# Implementation Fit Analysis: Physics-Guided BiGRU Architecture

This document assesses whether the current codebase implements the physics-guided BiGRU architecture as described in `Guide_gpt.md`, identifies gaps/deviations, and proposes concrete improvements. All citations below point to paths in this repository.

## Executive Summary

- Overall alignment vs <PERSON> (2024): Partial. The repository implements the BiGRU backbone and Mudrock pseudolabel prior, with end-to-end training, metrics, and plots. However, two required rock-physics constraints and two guidance strategies are not runnable, and the prescribed blind-testing protocol from <PERSON> is not automated. See [<PERSON> et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf](<PERSON>%20et%20al.%20-%202024%20-%20Rock-physics-guided%20machine%20learning%20for%20shear%20son.pdf).
- Key strengths: Clean separation of physics ([src/rock_physics/](src/rock_physics/)), modeling ([src/models/](src/models/)), pipelines ([src/pipelines/](src/pipelines/)), and utilities ([src/utils/](src/utils/)). Pseudolabel training includes early stopping and gradient clipping; plots include R²/MAE/RMSE overlays.
- High-impact gaps vs <PERSON> (summary):
  - Missing rock-physics constraints II and III (empirical VP–VS linear fit; multiparameter regression).
  - Guidance strategies incomplete: physics-guided loss not wired as a runnable pipeline; transfer learning not implemented.
  - Blind-test protocol (train on one well, blind on four; average across five runs) not automated.
  - Baseline loss mismatch (paper: MAE; code: MSE) and lack of feature-set sweep including CNC.

See “High-impact gaps vs Zhao (2024)” below for details and citations.

## High-impact gaps vs Zhao (2024)

1) Rock-physics constraints beyond Mudrock are missing
- In Zhao: Three constraints are used — mudrock line (eq. 5), empirical VP–VS linear fit (eq. 6), and multiparameter regression (eq. 7). See [Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf](Zhao%20et%20al.%20-%202024%20-%20Rock-physics-guided%20machine%20learning%20for%20shear%20son.pdf).
- In code: Only the mudrock prior exists at [src/rock_physics/mudrock.py](src/rock_physics/mudrock.py); there are no selectable empirical VP–VS or multiparameter regression priors under [src/rock_physics/](src/rock_physics/).
- Impact: The core comparative methodology and the reported best-performing configuration cannot be reproduced.

2) Guidance strategies not runnable
- In Zhao: Three strategies — physics-guided pseudolabels, physics-guided loss, and transfer learning. See [Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf](Zhao%20et%20al.%20-%202024%20-%20Rock-physics-guided%20machine%20learning%20for%20shear%20son.pdf).
- In code: Pseudolabel baseline is present. The physics-guided loss is defined in [src/models/losses.py](src/models/losses.py) but not exposed as a training pipeline under [src/pipelines/](src/pipelines/). Transfer learning is not implemented or orchestrated in [bigru_training_with_outputs.py](bigru_training_with_outputs.py).
- Impact: Strategy comparisons and benefits described by Zhao cannot be replicated.

3) Blind-test protocol not automated
- In Zhao: Train on one well; blind-test on the remaining four; repeat for each well as the training well; report averaged metrics across five train-well choices. See [Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf](Zhao%20et%20al.%20-%202024%20-%20Rock-physics-guided%20machine%20learning%20for%20shear%20son.pdf).
- In code: [bigru_training_with_outputs.py](bigru_training_with_outputs.py) performs a single training configuration; there is no automated cross-well loop and averaging.
- Impact: Generalization claims are not directly comparable to Zhao’s protocol.

4) Best-performing method not reproducible
- In Zhao: The combination “multiparameter regression constraint + physics-guided pseudolabels” yields the largest blind-test improvement (≈47% RMSE reduction). See [Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf](Zhao%20et%20al.%20-%202024%20-%20Rock-physics-guided%20machine%20learning%20for%20shear%20son.pdf).
- In code: The multiparameter regression prior is absent, so this top configuration cannot be evaluated.
- Impact: Missing the study’s headline result pathway.

5) Loss function mismatch for baseline training
- In Zhao: Table 1 lists MAE as the supervised training loss; MSE is used within the physics-guided loss derivation. See [Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf](Zhao%20et%20al.%20-%202024%20-%20Rock-physics-guided%20machine%20learning%20for%20shear%20son.pdf).
- In code: The pseudolabel training uses MSE in [src/pipelines/pseudolabel_training.py](src/pipelines/pseudolabel_training.py) and orchestrator logic in [bigru_training_with_outputs.py](bigru_training_with_outputs.py).
- Impact: Reported errors may not be directly comparable to Zhao’s MAE-trained baseline.

6) Normalization policy inconsistency across wells
- In Zhao: “All parameters and all training wells are normalized to the range of −1 to 1.” See [Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf](Zhao%20et%20al.%20-%202024%20-%20Rock-physics-guided%20machine%20learning%20for%20shear%20son.pdf).
- In code: Min–max normalization is recomputed per call; scalers are not persisted/reused for blind wells in [src/utils/las_processing.py](src/utils/las_processing.py).
- Impact: Train/test scale mismatch during blind-well inference weakens parity with Zhao.

7) Resistivity log base discrepancy
- In Zhao: Resistivity is transformed using base-10 logarithm prior to modeling. See [Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf](Zhao%20et%20al.%20-%202024%20-%20Rock-physics-guided%20machine%20learning%20for%20shear%20son.pdf).
- In code: Natural logarithm is applied in [src/utils/las_processing.py](src/utils/las_processing.py).
- Impact: Input scaling differs from the methodology described by Zhao.

8) Feature-set sweep and CNC not reproduced
- In Zhao: Four input groups are evaluated (VP+GR; VP+GR+DEN; VP+GR+DEN+RES; VP+GR+DEN+RES+CNC), and VP+GR+DEN+RES is selected as best. See [Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf](Zhao%20et%20al.%20-%202024%20-%20Rock-physics-guided%20machine%20learning%20for%20shear%20son.pdf).
- In code: Fixed feature set; no CNC handling or automated feature ablation in [src/utils/las_processing.py](src/utils/las_processing.py) or [bigru_training_with_outputs.py](bigru_training_with_outputs.py).
- Impact: The feature-selection step central to Zhao is not reproduced.

9) Learning-rate scheduling details undocumented/absent
- In Zhao: ReduceLROnPlateau with specific patience and factor is reported in Table 1. See [Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf](Zhao%20et%20al.%20-%202024%20-%20Rock-physics-guided%20machine%20learning%20for%20shear%20son.pdf).
- In code: No explicit LR scheduler is visible in [src/pipelines/pseudolabel_training.py](src/pipelines/pseudolabel_training.py) (early stopping is present).
- Impact: Training dynamics may diverge from the reference setup, affecting comparability.

## Architecture Mapping (Guide → Implementation)

- **Physics prior (Mudrock line):** `src/rock_physics/mudrock.py` implements `VS_hat = (VP - 1.36)/1.16` (km/s). Used in training and inference via `MudrockLine.estimate_vs()` in `bigru_training_with_outputs.py`.
- **Model (BiGRU):** `src/models/networks.py` defines `BiGRUNet` (bidirectional GRU + linear head, tanh activation). Called from both the pipeline and orchestrator.
- **Pipeline (pseudolabels):** `src/pipelines/pseudolabel_training.py` augments features with `VS_hat` and trains with MSE, early stopping, gradient clipping.
- **Loss (physics-guided/min):** `src/models/losses.py` implements the adaptive min formulation (available but not used in current training script).
- **Data utilities:** `src/utils/las_processing.py` loads LAS, applies log transform to `res`, masks finite rows, normalizes features to (−1, 1), handles unit conversion for VP/VS.
- **Orchestration:** `bigru_training_with_outputs.py` builds train/test sets from LAS, runs training, computes metrics, and writes plots/CSV/report.

## Evidence of Alignment

- **Physics pseudolabel computed and appended:**
  - Training: `bigru_training_with_outputs.py` lines around
    - `phys = MudrockLine()` → `vs_phys_i = phys.estimate_vs({"vp": vp_i})`
    - `X_aug = np.hstack([Xn, vs_phys[:, None]])` before inference
  - Test: same pattern with `vs_phys_t` and `X_aug_t`
- **BiGRU architecture:** `src/models/networks.py`
  - `nn.GRU(..., bidirectional=True)` and `nn.Linear(2*hidden_dim, 1)`; tanh activation by default.
- **Sequence training:** `src/pipelines/pseudolabel_training.py`
  - Inputs shaped `[1, T, F]`, MSE loss, Adam optimizer, gradient clipping, early stopping with `patience` and `min_epochs`.
- **Preprocessing:** `src/utils/las_processing.py`
  - Natural log transform for resistivity, min–max normalization to (−1, 1), unit consistency checks (`Converted P-WAVE from m/s to km/s`, `Converted S-WAVE from m/s to km/s`).
- **Metrics and plots:** `bigru_training_with_outputs.py`
  - Correlation, RMSE, MAE in `results`; plots overlay R²/MAE/RMSE for BiGRU and physics baselines in `create_visualizations()` and `create_test_visualizations()`.
- **Tests:**
  - `tests/test_mudrock.py`: mudrock formula check.
  - `tests/test_losses.py`: physics-guided loss min formulation.
  - `tests/test_las_processing.py`: checks for curve extraction, masking, normalization, and unit conversion.

## Deviations and Gaps vs Guide

1) **Normalization across wells**
- Guide recommendation: fit normalization on training well(s) only; reuse the same scaler for blind wells.
- Current implementation: `prep_features_with_target()` computes min–max per call. For test wells, normalization is refit on test distributions, leading to train/test scale mismatch.
- Impact: Can distort model inputs during blind-well inference, weakening generalization and interpretability.

2) **Alternative training strategies**
- Physics-guided loss: Function exists (`src/models/losses.py`) but no pipeline module like `loss_guided_training.py` is present.
- Transfer learning: Not implemented as a pipeline in `src/pipelines/`.
- Impact: Cannot directly switch strategies without writing glue code; the Guide encourages modular pipelines to simplify experimentation.

3) **Resistivity transform base**
- Guide: uses `log10` in examples; implementation uses natural `log` in both `src/utils/las_processing.py` and `src/data/loaders.py`.
- Impact: Minor if used consistently, but documentation should clearly state which base is used.

4) **Configuration**
- `configs/default.yaml` exists and `src/utils/config.py` provides a loader, but the main script does not consume YAML for features/targets/hyperparameters.
- Impact: Reduces reproducibility and portability of experiments described in the Guide.

5) **Random seed control**
- No explicit seed setting in `bigru_training_with_outputs.py` or pipeline.
- Impact: Non-reproducible runs across machines/sessions.

6) **Curve alias coverage**
- `src/utils/las_processing.get_curve()` requests specific mnemonics (e.g., only `"P-WAVE"` for VP, `"RT"` for resistivity).
- Impact: LAS files with different mnemonics (`VP`, `RHOB`, `GR`, `RES`, etc.) may be missed unless manually renamed. The Guide implies wider alias coverage for robustness.

## Recommendations (Prioritized)

1) **Persist and reuse normalization for blind wells**
- During training: compute and save per-feature `Xmin`, `Xmax` used to scale `Xn`.
- During inference: apply the saved `Xmin`, `Xmax` to test wells. Fall back to robust handling (clip or warn) if a feature falls outside the training range.
- Implementation option: return `(Xn, names, mask, Xmin, Xmax)` from training preprocessing and save in JSON.

2) **Add modular pipelines for other strategies**
- `src/pipelines/loss_guided_training.py`: wrap `physics_guided_loss()` with identical data shaping.
- `src/pipelines/transfer_learning.py`: two-stage routine (pretrain on `y_phys`, fine-tune on `y_true` with lower LR). Wire via a strategy flag.

3) **Make preprocessing choices explicit and configurable**
- Add a `log_base` option: `"ln"` vs `"log10"`. Use one consistently and record in artifacts.
- Control feature set and target via YAML; pass through to orchestrator.

4) **Integrate YAML config**
- Load `configs/default.yaml` at startup for features, target, and training hyperparameters.
- Record the resolved config alongside artifacts (already saving JSON; include config hash if desired).

5) **Seed control**
- Set NumPy and PyTorch seeds at script start; consider `torch.backends.cudnn.deterministic=True` where acceptable.

6) **Expand curve alias lists**
- Extend `get_curve()` to try common mnemonics (e.g., `VP`, `DTp`, `RHOB`, `GR`, `RES`, `RT`, `ILD`, `DEPTH`, `DEPT`). Optionally allow a mapping from YAML.

7) **Optional: save scaler and physics parameters**
- If local physics calibration is added, save slope/intercept used to compute `VS_hat`. Save normalization params in a small `scaler.json`.

## Nice-to-Haves

- **Windowed training option:** add a window sampler to improve stochasticity when training across multiple wells.
- **Metrics dashboard:** include MAPE or quantile-based metrics in the report; maintain unit-aware tolerances.
- **CI hooks:** run `pytest` and simple style checks on PRs.

## Closing Notes

The core architecture matches the Guide’s intent: physics guidance via Mudrock pseudolabels, a light-weight BiGRU for depth sequences, and clear outputs for QA. Implementing consistent normalization across wells, integrating YAML-driven configuration, and adding the alternative training pipelines would bring the implementation fully in line with the Guide and make experimentation easier.

# Expected ML Architecture: Physics-Guided BiGRU for VS Estimation

This chapter specifies the target ML architecture for predicting S-wave velocity (VS) from well logs. It formalizes inputs, preprocessing, the model, training, inference, and extensibility so that implementations can be validated against a single source of truth.

The design combines a physics prior (Mudrock line) with a lightweight bidirectional GRU (BiGRU) to leverage both domain knowledge and sequential patterns along depth.

## Architecture Diagram (Mermaid)

```mermaid
flowchart TD
  subgraph Inputs
    LAS["LAS files"]
    Curves["Extract curves: P-WAVE (vp), S-WAVE (vs, optional), GR (gr), RHOB (den), RT (res), DEPTH (depth)"]
  end

  LAS --> Curves

  subgraph Preprocessing
    Units["Unit checks: vp, vs → km/s (convert if median > 100)"]
    ResLog["Resistivity transform: res := ln(max(res, 1e-6))"]
    Mask["Finite mask alignment across features (+target if available)"]
    Norm["Min–max normalization to (−1, 1); record Xmin/Xmax"]
  end

  Curves --> Units --> ResLog --> Mask --> Norm

  subgraph Physics
    Mudrock["Mudrock line: VS_phys = (VP − 1.36)/1.16"]
    Augment["Append VS_phys as extra feature → X_aug (T × (F+1))"]
  end

  Norm --> Mudrock --> Augment

  subgraph Shape
    ShapeB["Shape to (B=1, T, F+1)"]
  end

  Augment --> ShapeB

  subgraph Model["BiGRUNet"]
    GRU["BiGRU encoder: nn.GRU(bidirectional=True)"]
    Act["Activation: Tanh (default) or ReLU"]
    Head["Linear(2H → 1) per time step"]
  end

  ShapeB --> GRU --> Act --> Head --> Yhat["Predicted VS ŷ (T,)"]

  subgraph Training
    MSE["Loss: MSE(ŷ, y_true)"]
    AltLoss["Alternative: physics_guided_loss(ŷ, y_true, y_phys)"]
    Opt["Adam (lr=1e−3), grad clip (1.0), early stopping (patience, min_epochs)"]
  end

  Yhat --> MSE
  Yhat -. optional .-> AltLoss
  MSE --> Opt
  AltLoss --> Opt

  subgraph Inference
    PreT["Apply same preprocessing; reuse training Xmin/Xmax if persisted"]
    Predict["Forward pass → ŷ (T,)"]
  end

  Augment --> PreT --> Predict

  subgraph Outputs
    CSV["predictions_vs_actual.csv (train) / predictions_<well>.csv (test)"]
    Plots["Overview + error plots (training and per-test)"]
    Metrics["MAE, RMSE, R², Corr; summary_report.txt"]
    ModelP["bigru_model.pth (trained weights)"]
    Config["training_config.json; scaler.json (optional)"]
  end

  Opt -->|"trained model"| ModelP
  Predict --> CSV
  Predict --> Plots
  Predict --> Metrics
```

## 1) Objectives and Scope

- Task: Regress S-wave velocity VS (km/s) from standard well logs.
- Constraints: Respect known rock-physics trends via the Mudrock line; operate on depth-ordered sequences; be robust to missing curves and unit differences.
- Outputs: Per-depth predictions of VS aligned to input samples, with metrics (MAE, RMSE, R², correlation) and QA plots.

## 2) Repository Interfaces and Responsibilities

- Physics prior: src/rock_physics/mudrock.py (MudrockLine.estimate_vs)
- Model: src/models/networks.py (BiGRUNet)
- Losses: src/models/losses.py (physics_guided_loss)
- Training pipeline: src/pipelines/pseudolabel_training.py (train_with_pseudolabels)
- LAS utilities and preprocessing: src/utils/las_processing.py
- Orchestration and reporting: bigru_training_with_outputs.py

## 3) Inputs and Preprocessing

3.1 Curves and mnemonics
- Required (for physics prior): VP (P-WAVE) → key: "vp"
- Optional/additional features (when present): GR ("gr"), RHOB density ("den"), Resistivity ("res").
- Depth reference (optional but recommended for plotting/alignment): DEPTH ("depth").
- Current mnemonic set is strict ("P-WAVE", "S-WAVE", "GR", "RHOB", "RT", "DEPTH"); consider expanding aliases (e.g., VP/DTp/DTCO for VP proxies; RES/ILD/RT for resistivity) in get_curve.

3.2 Unit handling
- VP and VS are expected in km/s. If median > 100, convert m/s → km/s (see convert_swave_units and VP conversion in load_las_dataframe).

3.3 Resistivity transform
- Apply log transform to resistivity input to stabilize heavy-tailed distributions: res := ln(max(res, 1e-6)).
- Note: Examples in the Guide sometimes use log10; the implementation currently uses natural log. Either is acceptable with consistent documentation.

3.4 Masking and normalization
- Build feature matrix X ∈ R^{T×F} from available columns among (vp, gr, den, res).
- Create a finite mask across all selected features and, if available, the target y to align all arrays.
- Normalize features feature-wise to (−1, 1) via min–max:
  Xn = 2 * (X − Xmin) / (Xmax − Xmin + ε) − 1, with ε = 1e−9.
- Expected best practice for generalization: fit Xmin,Xmax on training data and reuse them for blind wells; persist to disk (e.g., scaler.json).

3.5 Physics prior feature (pseudolabel)
- Compute VS physics estimate using the Mudrock line (km/s):
  VS_phys = (VP − 1.36) / 1.16.
- Append VS_phys as an extra feature column to Xn → X_aug ∈ R^{T×(F+1)}.

## 4) Sequence Formation and Shapes

- Depth-ordered samples form a single sequence per well (or per concatenated training set).
- Training shapes (as implemented):
  - X_aug: (T, F+1)
  - Add batch dimension B=1 for GRU: xb: (B=1, T, F+1)
  - Target y: (T,) → yb: (B=1, T)
- The model returns per-time-step predictions: ŷ: (B=1, T) → flattened to (T,).
- Optional (future): windowed training for stochastic mini-batches across wells.

## 5) Model Architecture (BiGRUNet)

5.1 Components
- Encoder: nn.GRU with batch_first=True and bidirectional=True.
- Nonlinearity: Tanh (default) or ReLU.
- Head: Linear layer mapping concatenated forward/backward hidden states (2·H) to scalar per step.

5.2 Default hyperparameters
- Hidden size H: 16
- GRU layers L: 1
- Activation: Tanh
- Input dim: in_dim = F + 1 (augmented physics feature)

5.3 Forward computation and shapes
- Input: x ∈ R^{B×T×(F+1)}
- GRU output: O ∈ R^{B×T×(2H)}
- Activation: A = act(O)
- Linear head: Z = W·A + b → R^{B×T×1}
- Squeeze last dim: ŷ ∈ R^{B×T}

5.4 Rationale
- Bidirectionality captures local upward/downward trends along depth.
- A single lightweight layer reduces overfitting risk given typical data volumes.
- Per-step head preserves resolution at each depth sample without pooling.

## Model Internals (Layer-by-Layer, Mermaid)

```mermaid
flowchart LR
  subgraph Input
    X["x: (B, T, F+1) — augmented features incl. VS_phys"]
  end

  X --> GRU["BiGRU: nn.GRU(bidirectional=True)\nOutput O: (B, T, 2H), H=16 by default"]

  subgraph BiGRU_Detail["BiGRU internals"]
    FWD["Forward GRU: (B, T, H)"]
    BWD["Backward GRU: (B, T, H)"]
    CONCAT["Concat [FWD | BWD]: (B, T, 2H)"]
  end

  GRU --> FWD
  GRU --> BWD
  FWD --> CONCAT
  BWD --> CONCAT

  CONCAT --> ACT["Activation (Tanh or ReLU): (B, T, 2H)"]
  ACT --> FC["Linear head per time step: Linear(2H → 1)\nZ: (B, T, 1)"]
  FC --> SQUEEZE["Squeeze last dim"]
  SQUEEZE --> YHAT["ŷ: (B, T)"]

  %% Notes
  note over X: B = batch size (1 in current pipeline),\nT = sequence length (depth samples), F = feature count.
  note right of FC: Head applies the same linear map at each time step.
```

## 6) Training Strategies

6.1 Baseline (pseudolabel feature + MSE)
- Loss: MSE(ŷ, y) averaged over time steps T.
- Optimizer: Adam(lr=1e−3).
- Regularization: gradient clipping (max_norm=1.0) and early stopping with patience, enforcing a minimum number of epochs.
- Steps per epoch can repeat updates over the full sequence to accelerate convergence on small datasets.

6.2 Physics-guided loss (alternative)
- From Zhao et al. (2024), implemented in src/models/losses.py:
  L = MSE(ŷ, y) + min(MSE(ŷ, y), MSE(ŷ, y_phys)).
- Encourages predictions near both the ground truth and the physics estimate when applicable.
- Expected to be wired as an alternative training pipeline (e.g., src/pipelines/loss_guided_training.py).

6.3 Transfer learning (alternative)
- Stage 1: pretrain on y_phys as target to encode physics trend.
- Stage 2: fine-tune on y_true with a lower learning rate.
- Useful when labeled VS is sparse; can be added as src/pipelines/transfer_learning.py.

6.4 Reproducibility
- Set seeds for NumPy and PyTorch; consider torch.backends.cudnn.deterministic=True where appropriate.

## 7) Inference and Outputs

- Preprocess test well identically to training (reuse training Xmin,Xmax for normalization if persisted).
- Compute VS_phys for the test well and append to features.
- Shape inputs to (1, T, F+1), run forward pass, flatten to (T,).
- Save artifacts:
  - Predictions CSV with depth alignment and (optionally) error columns when targets are available.
  - Overview and error comparison plots for QA (BiGRU vs. physics).
  - Metrics summary (MAE, RMSE, R², correlation) and a text report.

## 8) Extensibility Points

- Feature set: expand curve aliases; add feature selection via YAML.
- Normalization: persist and reuse; optionally switch to robust scalers for outlier resilience.
- Physics prior: allow calibrated slope/intercept per formation; persist parameters.
- Model variants: stacked GRU layers, dropout, or attention pooling if data volume supports it.
- Pipelines: plug-in strategy flag to switch among baseline, physics-guided loss, and transfer learning.

## 9) Acceptance Criteria (for implementation conformance)

A build is considered conformant when the following are true:
- Uses Mudrock VS_phys as an appended feature in training and inference.
- Operates on depth-ordered sequences and predicts per time step.
- Matches BiGRUNet interface and default hyperparameters unless overridden via configuration.
- Applies the documented preprocessing (log on resistivity, unit handling, min–max normalization) and documents the chosen log base.
- Provides metrics, CSV outputs, and plots as described.
- Reuses training normalization for blind wells or explicitly documents the deviation and rationale.

# Zhao (2024) Conformance Summary

Updated: 2025-09-20

Scope and basis
- Paper: [Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf](Zhao%20et%20al.%20-%202024%20-%20Rock-physics-guided%20machine%20learning%20for%20shear%20son.pdf)
- Code modules reviewed:
  - Model: [src/models/networks.py](src/models/networks.py), [src/models/losses.py](src/models/losses.py)
  - Pipelines: [src/pipelines/pseudolabel_training.py](src/pipelines/pseudolabel_training.py)
  - Physics prior: [src/rock_physics/mudrock.py](src/rock_physics/mudrock.py)
  - Data utils: [src/utils/las_processing.py](src/utils/las_processing.py), [src/data/loaders.py](src/data/loaders.py)
  - Orchestration/config: [bigru_training_with_outputs.py](bigru_training_with_outputs.py), [configs/default.yaml](configs/default.yaml), [src/utils/config.py](src/utils/config.py)
  - Tests: [tests/test_mudrock.py](tests/test_mudrock.py), [tests/test_losses.py](tests/test_losses.py), [tests/test_networks.py](tests/test_networks.py), [tests/test_las_processing.py](tests/test_las_processing.py), [tests/test_pseudolabel_pipeline.py](tests/test_pseudolabel_pipeline.py)

Executive verdict
- Overall alignment: 72% (good baseline; incomplete on two physics-guided strategies and hybrid priors).
- Strengths: Correct BiGRU backbone; Mudrock pseudolabel feature; end-to-end training/inference with metrics and plots; physics-guided loss function exists as an API.
- Gaps blocking parity: Missing runnable pipelines for physics-guided loss and transfer learning; missing empirical VP–VS and multiparameter regression priors.

Implemented (aligned with the paper)
- BiGRU sequence model and per-depth head
  - Bidirectional GRU with linear head matches paper intent. See [src/models/networks.py](src/models/networks.py).
- Physics-guided pseudolabels (Mudrock line)
  - VS_phys = (VP − 1.36)/1.16 computed and appended as input during training and inference. See [src/rock_physics/mudrock.py](src/rock_physics/mudrock.py), [src/pipelines/pseudolabel_training.py](src/pipelines/pseudolabel_training.py), [bigru_training_with_outputs.py](bigru_training_with_outputs.py).
- Training loop and evaluation
  - Supervised MSE, Adam, gradient clipping, early stopping; outputs include CSVs, plots, and metrics (MAE/RMSE/R²/corr). See [bigru_training_with_outputs.py](bigru_training_with_outputs.py).
- Physics-guided loss function (API available)
  - Adaptive min formulation implemented and unit-tested. See [src/models/losses.py](src/models/losses.py), [tests/test_losses.py](tests/test_losses.py).
- Data handling and features
  - LAS loading, VP/VS unit conversion to km/s, ln(resistivity), finite masking, min–max normalization. See [src/utils/las_processing.py](src/utils/las_processing.py), [src/data/loaders.py](src/data/loaders.py).

Missing or incomplete (paper-critical)
- Physics-guided loss (runnable)
  - Function exists but not wired into a selectable training pipeline in [src/pipelines/](src/pipelines/).
- Transfer learning
  - No two-stage routine (pretrain on VS_phys → fine-tune on VS_true with reduced LR) exposed in [bigru_training_with_outputs.py](bigru_training_with_outputs.py).
- Hybrid rock-physics priors beyond Mudrock
  - Empirical VP–VS (paper eq. 6) and multiparameter regression (paper eq. 7) not implemented as selectable priors under [src/rock_physics/](src/rock_physics/).
- Cross-well normalization and reproducibility
  - Normalization recomputed per well; training scalers not persisted/reused for blind wells. Seeds not set; YAML not authoritative. See [src/utils/las_processing.py](src/utils/las_processing.py), [configs/default.yaml](configs/default.yaml), [src/utils/config.py](src/utils/config.py).
- Input details and documentation
  - Narrow LAS alias coverage; CNC not included; resistivity uses natural log while the paper cites log10 — should be configurable and documented.

Single most critical gap (blocker)
- Missing runnable pipelines for physics-guided loss and transfer learning
  - These are two of the three guidance strategies in Zhao (2024); without runnable options, the core comparative methodology cannot be reproduced.

Top 1–3 next actions (prioritized)
1) Implement and expose physics-guided loss and transfer learning pipelines; add a strategy flag in [bigru_training_with_outputs.py](bigru_training_with_outputs.py).
2) Add hybrid priors (empirical VP–VS and multiparameter regression) with calibration on training well(s); implement under [src/rock_physics/](src/rock_physics/) and make selectable.
3) Enforce fair preprocessing and reproducibility: persist training scaler for blind wells, set NumPy/PyTorch seeds, and make YAML authoritative via [configs/default.yaml](configs/default.yaml) and [src/utils/config.py](src/utils/config.py). Optionally include CNC and broaden LAS aliases in [src/utils/las_processing.py](src/utils/las_processing.py).

Alignment breakdown (percentages)
- Network architecture: 95%
- Physics-guided strategies: 40%
- Hybrid physics priors: 35%
- Feature engineering and preprocessing: 70%
- Training/evaluation protocol: 80%
- Reproducibility/configuration: 50%
- Overall: 72%

Evidence pointers
- Physics prior: [src/rock_physics/mudrock.py](src/rock_physics/mudrock.py)
- Model: [src/models/networks.py](src/models/networks.py), [src/models/losses.py](src/models/losses.py)
- Pipeline: [src/pipelines/pseudolabel_training.py](src/pipelines/pseudolabel_training.py)
- Utilities: [src/utils/las_processing.py](src/utils/las_processing.py), [src/data/loaders.py](src/data/loaders.py), [src/utils/config.py](src/utils/config.py)
- Orchestration: [bigru_training_with_outputs.py](bigru_training_with_outputs.py)
- Config: [configs/default.yaml](configs/default.yaml)
- Tests: [tests/test_mudrock.py](tests/test_mudrock.py), [tests/test_losses.py](tests/test_losses.py), [tests/test_networks.py](tests/test_networks.py), [tests/test_las_processing.py](tests/test_las_processing.py), [tests/test_pseudolabel_pipeline.py](tests/test_pseudolabel_pipeline.py)
