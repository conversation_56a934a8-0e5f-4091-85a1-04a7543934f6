# Repository Guidelines

## Project Structure & Module Organization
Source code lives under `src/`, split into focused packages: `data/` for loaders, `models/` for BiGRU networks and custom losses, `pipelines/` for training loops, `rock_physics/` for mudrock and future physics models, and `utils/` for LAS helpers. Defaults and experiment knobs sit in `configs/default.yaml`. The orchestrator `bigru_training_with_outputs.py` should be run from the repo root, while exploratory notebooks belong in `archives/` to keep the tree clean. Place raw LAS inputs inside `Las/`, and mirror new tests under `tests/` so structure aligns with the modules they verify.

## Build, Test, and Development Commands
Create an isolated environment before installing dependencies:
```bash
python -m venv .venv
.\.venv\Scripts\activate    # PowerShell
pip install -r requirements.txt
```
Run the full training workflow with `python bigru_training_with_outputs.py`; results appear in `bigru_results_<timestamp>/`. Execute unit tests with `pytest` and use filters like `pytest -k pipelines` to target a specific area. Regenerate plots or reports by rerunning the training script after updating code or LAS inputs.

## Coding Style & Naming Conventions
Follow PEP 8 with 4-space indentation. Use snake_case for functions and variables, PascalCase for classes (`MudrockLine`), and SCREAMING_SNAKE_CASE for constants. Keep modules cohesive; add helper functions to existing files rather than creating single-function modules. Format touched files with `black` and document domain assumptions (units, curve aliases) in docstrings or inline comments when ambiguity might arise.

## Testing Guidelines
Name new test files `test_<module>.py` and place them in `tests/`. Leverage `pytest` fixtures for sample LAS data and cover unit conversions, masking logic, and physics estimates with tolerance-based assertions. When adding pipelines or plotting utilities, include smoke tests that confirm artifacts (CSV rows, PNG creation) without relying on full datasets.

## Commit & Pull Request Guidelines
Write imperative, descriptive commit messages such as `Refine mudrock preprocessing masks`. Pull requests should summarize motivation, note key code paths touched, and list verification steps (`pytest`, manual plot review). Link related issues or research tickets and attach representative output snippets or figures when behavior changes are visual.
