"""Data loading utilities."""
from __future__ import annotations

from typing import Sequence, <PERSON><PERSON>

import numpy as np
import pandas as pd


def load_logs_csv(path: str, feature_cols: Sequence[str], target_col: str) -> Tuple[np.ndarray, np.ndarray]:
    """Load log data from a CSV file and normalize features to (-1, 1).

    Parameters
    ----------
    path : str
        Path to the CSV file.
    feature_cols : Sequence[str]
        Names of feature columns to use.
    target_col : str
        Name of the target column (true VS).
    """
    df = pd.read_csv(path)
    if "res" in feature_cols and "res" in df.columns:
        # Use pandas clip with 'lower' kw to support all versions
        df["res"] = np.log(df["res"].clip(lower=1e-6))

    X = df[list(feature_cols)].to_numpy(dtype=float)
    y = df[target_col].to_numpy(dtype=float)

    # Normalize features to (-1, 1)
    X_min = X.min(axis=0)
    X_max = X.max(axis=0)
    Xn = 2 * (X - X_min) / (X_max - X_min + 1e-9) - 1
    return Xn.astype(np.float32), y.astype(np.float32)
