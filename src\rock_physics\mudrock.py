"""Mudrock line implementation."""
from __future__ import annotations

from typing import Dict
import numpy as np

from .base import RockPhysicsModel


class MudrockLine(RockPhysicsModel):
    """Mudrock line relating P-wave and S-wave velocity."""

    name = "mudrock"

    def __init__(self, keys: tuple[str, ...] = ("vp",)) -> None:
        self.keys = keys

    def estimate_vs(self, logs: Dict[str, np.ndarray]) -> np.ndarray:
        """Estimate VS from VP using the mudrock line.

        Parameters
        ----------
        logs:
            Dictionary containing a "vp" key with P-wave velocity in km/s.
        """
        vp = logs["vp"]
        return (vp - 1.36) / 1.16
