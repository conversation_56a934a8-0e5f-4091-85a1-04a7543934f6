### Physics‑Guided ML Workflow for Shear Sonic Log Prediction (after <PERSON> et al., 2024)

This guideline shows how to implement a physics‑guided machine learning (ML) workflow in a code editor (VS Code or PyCharm) for predicting shear sonic logs. It begins with a concise theoretical overview grounded in <PERSON> et al. (2024), then translates those ideas into a modular, future‑proof Python codebase you can evolve to include additional rock‑physics models such as Xu<PERSON> or Self‑Consistent Approximation (SCA).

Reference context: <PERSON> et al., “Rock‑physics‑guided machine learning for shear sonic log prediction,” Geophysics 89(1), 2024, DOI:10.1190/geo2023‑0152.1.

#### What you will build

You will implement: (1) a basic rock‑physics model (the mudrock line) and a clean interface to add more models; (2) a bi‑GRU neural network suited for depth‑sequence logs; (3) three physics‑guided strategies from <PERSON> (2024): physics‑guided pseudolabels, a physics‑guided loss function, and transfer learning; and (4) a project skeleton with tests, docs, and CI.

---

### 1) Theoretical foundation

#### The mudrock line and why it helps

For clastic rocks (sandstone–shale mixtures), the mudrock line (<PERSON><PERSON><PERSON> et al., 1985) provides a first‑order relationship between P‑wave and S‑wave velocities. <PERSON> (2024) use this simple law as one of several rock‑physics constraints to guide ML training.

The relationship is given by:

\[
V_P = 1.16\,V_S + 1.36 \quad \text{(km/s)}
\]

Rearranged to estimate S‑wave velocity from measured \(V_P\):

\[
\hat V_S^\text{mudrock} = \frac{V_P - 1.36}{1.16}
\]

Even though the mudrock line does not capture local geological nuances, Zhao (2024) show that injecting this first‑order physics into training improves accuracy and generalization relative to pure ML.

#### Physics‑guided ML strategies used by Zhao (2024)

Zhao (2024) test three complementary strategies for using rock‑physics constraints inside the ML training loop:

- Physics‑guided pseudolabels: Compute a rock‑physics estimate of S‑wave velocity (e.g., \(\hat V_S^\text{mudrock}\)) and append it to the ML inputs as an additional feature. The ML model learns from both raw logs (e.g., GR, DEN, RES, VP) and the physics estimate, which often improves generalization with limited labels.

- Physics‑guided loss function: In addition to the data loss, penalize deviations from the rock‑physics estimate. Zhao (2024) propose an adaptive combination:
  
  \[
  \text{lossa}=\frac{1}{n}\sum_i (y^\text{pred}_i - y^\text{true}_i)^2,\quad
  \text{lossb}=\frac{1}{n}\sum_i (y^\text{pred}_i - y^\text{phys}_i)^2
  \]
  \[
  \text{Loss}=\text{lossa}+\min(\text{lossa}, \text{lossb})
  \]
  
  This formulation adaptively emphasizes whichever term is smaller per batch and performed better than fixed weights in their experiments.

- Transfer learning: Pretrain the network to match the physics‑based label (e.g., \(\hat V_S^\text{mudrock}\)) using conventional logs as inputs, then fine‑tune on the true \(V_S\) with a reduced learning rate. This injects domain knowledge into the initialization to speed and steady convergence.

Zhao (2024) evaluate three rock‑physics constraints: mudrock line; an empirical VP–VS relation fitted to the training well; and a multiparameter regression using VP, DEN, RES, and GR. They find all physics‑guided approaches beat pure ML, and that the multiparameter regression plus pseudolabels performs best overall. Nonetheless, even the simple mudrock line improves generalization and is a good starting point.

---

### 2) Codebase design

#### Suggested project structure

Organize the repository so physics, data, and modeling are cleanly separated. This makes it trivial to add new rock‑physics models or swap ML backends.

```
.
├── src/
│   ├── rock_physics/
│   │   ├── base.py
│   │   ├── mudrock.py
│   │   ├── empirical_vpvs.py
│   │   └── multiparam.py
│   ├── data/
│   │   ├── loaders.py
│   │   └── transforms.py
│   ├── models/
│   │   ├── networks.py
│   │   ├── losses.py
│   │   └── train.py
│   ├── utils/
│   │   └── config.py
│   └── pipelines/
│       ├── pseudolabel_training.py
│       ├── loss_guided_training.py
│       └── transfer_learning.py
├── configs/
│   └── default.yaml
├── tests/
│   ├── test_mudrock.py
│   └── test_losses.py
├── notebooks/               (optional for EDA)
├── requirements.txt
├── pyproject.toml           (optional; black/isort/mypy)
└── README.md
```

This structure is modular and expandable: to add Xu‑White or SCA later, create a new file in src/rock_physics and wire it into pipelines without refactoring core training code.

#### Abstract interface for rock‑physics models

Define a small interface every rock‑physics model must implement. This keeps the ML code agnostic to the specific physics.

```python
# src/rock_physics/base.py
from __future__ import annotations
from abc import ABC, abstractmethod
from typing import Dict
import numpy as np

class RockPhysicsModel(ABC):
    name: str = "base"

    @abstractmethod
    def estimate_vs(self, logs: Dict[str, np.ndarray]) -> np.ndarray:
        """
        Return a physics-based estimate of VS (km/s) given logs.
        logs contains arrays keyed by column names (e.g., 'vp','gr','den','res').
        The returned array must be 1D, same length as logs['vp'].
        """
        raise NotImplementedError

    def calibrate(self, logs: Dict[str, np.ndarray], vs_true: np.ndarray) -> None:
        """
        Optionally fit model parameters to local training data (no-op by default).
        """
        return
```

#### Step‑by‑step: implement the mudrock line model

1) Create the mudrock model class that maps VP to VS using the mudrock line.  
2) Ensure units are consistent with the paper (km/s).  
3) Provide optional calibration hooks if you decide to locally regress the slope/intercept on a training well later.

```python
# src/rock_physics/mudrock.py
from __future__ import annotations
import numpy as np
from typing import Dict, Optional
from .base import RockPhysicsModel

class MudrockLine(RockPhysicsModel):
    name = "mudrock_line"

    def __init__(self, slope: float = 1.16, intercept: float = 1.36, vp_key: str = "vp"):
        self.slope = slope
        self.intercept = intercept
        self.vp_key = vp_key

    def estimate_vs(self, logs: Dict[str, np.ndarray]) -> np.ndarray:
        vp = logs[self.vp_key].astype(float)
        return (vp - self.intercept) / self.slope

    def calibrate(self, logs: Dict[str, np.ndarray], vs_true: np.ndarray) -> None:
        """
        Optional: fit slope/intercept via least squares on the training well.
        Solve vp ≈ s * vs + c  =>  minimize ||[vs, 1] [s, c]^T - vp||^2
        """
        vp = logs[self.vp_key].astype(float)
        vs = vs_true.astype(float)
        A = np.vstack([vs, np.ones_like(vs)]).T
        s, c = np.linalg.lstsq(A, vp, rcond=None)[0]
        # Re-derive slope/intercept to VS = (VP - c) / s if desired.
        self.slope = float(s)
        self.intercept = float(c)
```

#### Templates for additional models (e.g., Xu‑White, SCA)

You can add any rock‑physics model by subclassing RockPhysicsModel. For example:

```python
# src/rock_physics/empirical_vpvs.py
import numpy as np
from typing import Dict
from .base import RockPhysicsModel

class EmpiricalVPVS(RockPhysicsModel):
    """
    VS = a * VP + b  (fitted on the training well)
    """
    name = "empirical_vpvs"

    def __init__(self, a: float | None = None, b: float | None = None, vp_key: str = "vp"):
        self.a = a
        self.b = b
        self.vp_key = vp_key

    def calibrate(self, logs: Dict[str, np.ndarray], vs_true: np.ndarray) -> None:
        vp = logs[self.vp_key].astype(float)
        A = np.vstack([vp, np.ones_like(vp)]).T
        a, b = np.linalg.lstsq(A, vs_true.astype(float), rcond=None)[0]
        self.a, self.b = float(a), float(b)

    def estimate_vs(self, logs: Dict[str, np.ndarray]) -> np.ndarray:
        assert self.a is not None and self.b is not None, "Call calibrate() before use."
        vp = logs[self.vp_key].astype(float)
        return self.a * vp + self.b
```

```python
# src/rock_physics/multiparam.py
import numpy as np
from typing import Dict, Sequence
from .base import RockPhysicsModel

class MultiParamRegression(RockPhysicsModel):
    """
    VS = a*GR + b*DEN + c*VP + d*RES + e  (fitted on the training well)
    """
    name = "multi_param"

    def __init__(self, keys: Sequence[str] = ("gr", "den", "vp", "res")):
        self.keys = list(keys)
        self.coeffs: np.ndarray | None = None  # shape (len(keys)+1,)

    def calibrate(self, logs: Dict[str, np.ndarray], vs_true: np.ndarray) -> None:
        X = np.vstack([logs[k].astype(float) for k in self.keys]).T  # [N, p]
        X_aug = np.hstack([X, np.ones((X.shape[0], 1))])            # add intercept
        self.coeffs, *_ = np.linalg.lstsq(X_aug, vs_true.astype(float), rcond=None)

    def estimate_vs(self, logs: Dict[str, np.ndarray]) -> np.ndarray:
        assert self.coeffs is not None, "Call calibrate() before use."
        X = np.vstack([logs[k].astype(float) for k in self.keys]).T
        X_aug = np.hstack([X, np.ones((X.shape[0], 1))])
        return X_aug @ self.coeffs
```

This interface lets you plug in a full Xu‑White or SCA model later; simply implement calibrate (if needed) and estimate_vs using your model’s equations.

#### Best‑practice recommendations

Prefer small, single‑responsibility modules; keep the ML model agnostic to the specific rock physics by depending only on the RockPhysicsModel interface; use type hints and docstrings; add unit tests for physics models and the loss function; store config in YAML; keep experiments reproducible with seed control and normalized preprocessing.

---

### 3) Practical implementation in a code editor

#### Environment and editor setup

In VS Code or PyCharm, create and activate a virtual environment, then install dependencies.

```bash
# in your project root
python -m venv .venv
# Windows: .venv\Scripts\activate
# macOS/Linux:
source .venv/bin/activate

pip install --upgrade pip
pip install numpy pandas scikit-learn torch pyyaml
# optional quality-of-life
pip install black isort mypy pytest
```

Recommended VS Code extensions: Python, Pylance, Jupyter (optional), YAML. In PyCharm, enable “Scientific Mode” for data exploration if desired.

#### Data preparation and transforms

Zhao (2024) normalize features to (−1, 1) and apply a base‑10 log transform to resistivity. They also found VP + GR + DEN + RES to be a strong feature set. Keep unit consistency (km/s for velocities).

```python
# src/data/transforms.py
import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler

class LogPreprocessor:
    def __init__(self, feature_keys=("vp","gr","den","res"), target_key="vs", log_res_keys=("res",)):
        self.feature_keys = list(feature_keys)
        self.target_key = target_key
        self.log_res_keys = set(log_res_keys)
        self.scaler = MinMaxScaler(feature_range=(-1, 1))

    def fit(self, df: pd.DataFrame) -> None:
        X = df[self.feature_keys].copy()
        for k in self.log_res_keys:
            if k in X:
                X[k] = np.log10(np.clip(X[k].values, 1e-6, None))
        self.scaler.fit(X.values)

    def transform(self, df: pd.DataFrame):
        X = df[self.feature_keys].copy()
        for k in self.log_res_keys:
            if k in X:
                X[k] = np.log10(np.clip(X[k].values, 1e-6, None))
        Xn = self.scaler.transform(X.values)
        y = df[self.target_key].values if self.target_key in df else None
        return Xn, y
```

If you train on one well and predict blind on others (as in Zhao, 2024), fit the scaler on the training well(s) only, then reuse it for blind wells.

#### Sequence modeling with a bi‑GRU

Treat logs as depth sequences. A simple bi‑GRU with a fully‑connected head mirrors Zhao’s setup (they used one bi‑GRU layer with 16 units and a dense layer).

```python
# src/models/networks.py
import torch
from torch import nn

class BiGRUNet(nn.Module):
    def __init__(self, in_dim: int, hidden_dim: int = 16, num_layers: int = 1, use_tanh: bool = True):
        super().__init__()
        self.gru = nn.GRU(
            input_size=in_dim, hidden_size=hidden_dim, num_layers=num_layers,
            batch_first=True, bidirectional=True
        )
        self.act = nn.Tanh() if use_tanh else nn.Identity()
        self.fc = nn.Linear(hidden_dim * 2, 1)

    def forward(self, x):  # x: [B, T, F]
        out, _ = self.gru(x)
        out = self.act(out)
        out = self.fc(out).squeeze(-1)  # [B, T]
        return out
```

For training stability, shuffle by windows (sliding patches in depth), but evaluate sequentially to preserve context.

#### Physics‑guided loss (min formulation)

```python
# src/models/losses.py
import torch
from torch.nn import functional as F

def physics_guided_loss(y_pred, y_true, y_phys, base="mse"):
    if base == "mae":
        lossa = F.l1_loss(y_pred, y_true)
        lossb = F.l1_loss(y_pred, y_phys)
    else:
        lossa = F.mse_loss(y_pred, y_true)
        lossb = F.mse_loss(y_pred, y_phys)
    return lossa + torch.minimum(lossa, lossb)
```

Zhao (2024) report the min formulation outperforms fixed weights.

#### Training pipelines

Pseudolabels: append the physics estimate as a feature. Loss‑guided: compute y_phys and use the physics‑guided loss. Transfer learning: pretrain to y_phys, then fine‑tune to y_true with a 10× lower LR and no layer freezing.

```python
# src/pipelines/pseudolabel_training.py
import numpy as np
import torch
from torch import optim
from torch.utils.data import DataLoader, TensorDataset

def train_with_pseudolabels(model, Xn, y, vs_phys, lr=1e-3, epochs=50, patience=10, device="cpu"):
    X_aug = np.hstack([Xn, vs_phys[:, None]])  # add pseudolabel as feature
    ds = TensorDataset(torch.from_numpy(X_aug).float().unsqueeze(0),  # [1, T, F]
                       torch.from_numpy(y).float().unsqueeze(0))       # [1, T]
    dl = DataLoader(ds, batch_size=1, shuffle=False)
    opt = optim.Adam(model.parameters(), lr=lr)

    best_loss, bad_epochs = float("inf"), 0
    for epoch in range(epochs):
        for xb, yb in dl:
            xb, yb = xb.to(device), yb.to(device)
            yp = model(xb)
            loss = torch.nn.functional.mse_loss(yp, yb)
            opt.zero_grad(); loss.backward(); opt.step()
        if loss.item() + 1e-8 < best_loss:
            best_loss, bad_epochs = loss.item(), 0
        else:
            bad_epochs += 1
        if bad_epochs >= patience:
            break
    return model
```

```python
# src/pipelines/loss_guided_training.py
import torch
from torch import optim
from torch.utils.data import TensorDataset, DataLoader
from ..models.losses import physics_guided_loss

def train_with_physics_loss(model, Xn, y, y_phys, lr=1e-3, epochs=50, patience=10, device="cpu"):
    ds = TensorDataset(torch.from_numpy(Xn).float().unsqueeze(0),
                       torch.from_numpy(y).float().unsqueeze(0),
                       torch.from_numpy(y_phys).float().unsqueeze(0))
    dl = DataLoader(ds, batch_size=1, shuffle=False)
    opt = optim.Adam(model.parameters(), lr=lr)

    best, bad = float("inf"), 0
    for epoch in range(epochs):
        for xb, yb, ypb in dl:
            xb, yb, ypb = xb.to(device), yb.to(device), ypb.to(device)
            yp = model(xb)
            loss = physics_guided_loss(yp, yb, ypb, base="mse")
            opt.zero_grad(); loss.backward(); opt.step()
        if loss.item() + 1e-8 < best:
            best, bad = loss.item(), 0
        else:
            bad += 1
        if bad >= patience:
            break
    return model
```

```python
# src/pipelines/transfer_learning.py
import torch
from torch import optim
from torch.utils.data import TensorDataset, DataLoader

def transfer_learning(model, Xn, y_phys, y_true, lr_pre=1e-3, lr_ft=1e-4, epochs=30, patience=5, device="cpu"):
    # Pretrain to physics label
    ds_pre = TensorDataset(torch.from_numpy(Xn).float().unsqueeze(0),
                           torch.from_numpy(y_phys).float().unsqueeze(0))
    dl_pre = DataLoader(ds_pre, batch_size=1, shuffle=False)
    opt = optim.Adam(model.parameters(), lr=lr_pre)
    best, bad = float("inf"), 0
    for epoch in range(epochs):
        for xb, yb in dl_pre:
            xb, yb = xb.to(device), yb.to(device)
            yp = model(xb)
            loss = torch.nn.functional.mse_loss(yp, yb)
            opt.zero_grad(); loss.backward(); opt.step()
        if loss.item() + 1e-8 < best:
            best, bad = loss.item(), 0
        else:
            bad += 1
        if bad >= patience:
            break

    # Fine-tune to true label with reduced LR
    ds_ft = TensorDataset(torch.from_numpy(Xn).float().unsqueeze(0),
                          torch.from_numpy(y_true).float().unsqueeze(0))
    dl_ft = DataLoader(ds_ft, batch_size=1, shuffle=False)
    opt = optim.Adam(model.parameters(), lr=lr_ft)
    best, bad = float("inf"), 0
    for epoch in range(epochs):
        for xb, yb in dl_ft:
            xb, yb = xb.to(device), yb.to(device)
            yp = model(xb)
            loss = torch.nn.functional.mse_loss(yp, yb)
            opt.zero_grad(); loss.backward(); opt.step()
        if loss.item() + 1e-8 < best:
            best, bad = loss.item(), 0
        else:
            bad += 1
        if bad >= patience:
            break
    return model
```

#### Configuration

Use YAML for reproducibility and to switch strategies without code edits.

```yaml
# configs/default.yaml
seed: 42
features: ["vp", "gr", "den", "res"]
log_res_keys: ["res"]
normalization: "minmax_-1_1"
rock_physics:
  type: "mudrock_line"   # mudrock_line | empirical_vpvs | multi_param | xu_white | sca
  calibrate: false
model:
  in_dim_extra_pseudolabel: true
  hidden_dim: 16
  use_tanh: true
training:
  strategy: "pseudolabels"    # pseudolabels | physics_loss | transfer_learning
  lr: 0.001
  lr_finetune: 0.0001
  epochs: 50
  patience: 10
loss:
  base: "mse"                 # "mae" or "mse"
```

---

### 4) Future‑proofing the architecture

Design decisions that reduce refactoring:

- Keep the RockPhysicsModel interface minimal and stable. All new physics models (Xu‑White, SCA) only implement estimate_vs and optionally calibrate. The rest of the system stays unchanged.

- Contain training strategies in separate pipeline modules. You can add new strategies (e.g., physics‑informed regularizers, learnable physics parameters) without touching model code.

- Centralize configuration in YAML. New models or features are activated by config rather than code edits.

- Add unit tests for every new physics model and the physics‑guided loss. Tests pin behaviors and reduce regressions as you expand the codebase.

Practical software practices:

- Version control: adopt feature branches and semantic versioning; tag experiments with config hashes. Commit data schemas, not raw data.

- Dependency management: pin versions in requirements.txt or use a lockfile (pip‑tools or uv). Record CUDA/CPU variants for torch.

- Continuous integration: run tests and static checks (black, isort, mypy, pytest) on pull requests. Cache pip to speed builds. Example GitHub Actions workflow:

```yaml
# .github/workflows/ci.yml
name: CI
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with: { python-version: '3.11' }
      - run: python -m pip install --upgrade pip
      - run: pip install -r requirements.txt
      - run: pip install black isort mypy pytest
      - run: black --check src tests
      - run: isort --check-only src tests
      - run: mypy src
      - run: pytest -q
```

---

### 5) Examples

#### Example A: Implement and train with the mudrock line (pseudolabels)

This end‑to‑end example consumes a pandas DataFrame df with columns vp, gr, den, res, vs (km/s).

```python
import numpy as np, pandas as pd, torch
from src.data.transforms import LogPreprocessor
from src.rock_physics.mudrock import MudrockLine
from src.models.networks import BiGRUNet
from src.pipelines.pseudolabel_training import train_with_pseudolabels

# Prepare data (fit transforms on training well only)
pre = LogPreprocessor(feature_keys=("vp","gr","den","res"), target_key="vs", log_res_keys=("res",))
pre.fit(df_train)
Xn_train, y_train = pre.transform(df_train)
Xn_test,  y_test  = pre.transform(df_test)

# Physics pseudolabels from mudrock line
phys = MudrockLine()  # slope=1.16, intercept=1.36
vs_phys_train = phys.estimate_vs({k: df_train[k].values for k in ("vp","gr","den","res")})
vs_phys_test  = phys.estimate_vs({k: df_test[k].values  for k in ("vp","gr","den","res")})

# Model: input dims +1 for pseudolabel feature
in_dim = Xn_train.shape[1] + 1
model = BiGRUNet(in_dim=in_dim, hidden_dim=16, num_layers=1, use_tanh=True)

# Train with pseudolabel feature
device = "cuda" if torch.cuda.is_available() else "cpu"
model.to(device)
model = train_with_pseudolabels(model, Xn_train, y_train, vs_phys_train, lr=1e-3, epochs=50, patience=10, device=device)

# Evaluate (sequence inference)
with torch.no_grad():
    X_aug_test = np.hstack([Xn_test, vs_phys_test[:, None]])
    xb = torch.from_numpy(X_aug_test).float().unsqueeze(0).to(device)
    y_pred = model(xb).cpu().numpy().ravel()

# Metrics (RMSE)
rmse = np.sqrt(np.mean((y_pred - y_test)**2))
print(f"Test RMSE: {rmse:.3f} km/s")
```

You can switch to the physics‑guided loss or transfer learning by importing the corresponding pipeline and removing the pseudolabel feature column if desired.

#### Example B: Template stub for adding Xu‑White later

When you are ready to implement Xu‑White (Xu and White, 1995), follow this pattern:

```python
# src/rock_physics/xu_white.py
import numpy as np
from typing import Dict
from .base import RockPhysicsModel

class XuWhiteModel(RockPhysicsModel):
    name = "xu_white"

    def __init__(self, mineral_props: dict, fluid_props: dict, microstructure: dict, keys=("vp","gr","den","res")):
        self.mineral_props = mineral_props
        self.fluid_props = fluid_props
        self.microstructure = microstructure
        self.keys = list(keys)
        # initialize any precomputed tensors or parameters

    def calibrate(self, logs: Dict[str, np.ndarray], vs_true: np.ndarray) -> None:
        """
        Optional: fit uncertain parameters (e.g., aspect ratio distributions) to the training well.
        Could be a least-squares or Bayesian calibration using vs_true.
        """
        pass

    def estimate_vs(self, logs: Dict[str, np.ndarray]) -> np.ndarray:
        """
        Compute VS based on Xu-White effective medium theory using inputs from logs
        and configured mineral/fluid/microstructure properties. Return VS in km/s.
        """
        # TODO: implement the physics here
        raise NotImplementedError
```

Because your ML and training pipelines depend only on RockPhysicsModel, once estimate_vs is implemented, you can use XuWhiteModel in all three strategies with no changes elsewhere.

---

### Testing and documentation

Add quick unit tests to pin the physics and loss behaviors:

```python
# tests/test_mudrock.py
import numpy as np
from src.rock_physics.mudrock import MudrockLine

def test_mudrock_basic():
    phys = MudrockLine()
    vp = np.array([2.52, 3.48])  # km/s
    vs = phys.estimate_vs({"vp": vp})
    # Inverse relation: VS = (VP - 1.36) / 1.16
    expected = (vp - 1.36)/1.16
    assert np.allclose(vs, expected, atol=1e-6)
```

```python
# tests/test_losses.py
import torch
from src.models.losses import physics_guided_loss

def test_physics_loss_min():
    y_pred = torch.tensor([1.0, 2.0])
    y_true = torch.tensor([1.0, 2.0])
    y_phys = torch.tensor([0.0, 0.0])
    loss = physics_guided_loss(y_pred, y_true, y_phys, base="mse")
    # lossa=0; lossb>0; Loss = 0 + min(0, lossb) = 0
    assert torch.isclose(loss, torch.tensor(0.0))
```

Document assumptions (units, transforms), data schemas, and how to add new models in README.md. Consider a short “Reproduce Zhao (2024) choices” section: bi‑GRU(16), tanh activation, Adam optimizer, early stopping, learning‑rate reduction on plateau, feature set VP + GR + DEN + RES, resistivity log transform, and normalization to (−1, 1).

---

### Notes tied to Zhao (2024)

- Sequence model: Zhao (2024) used a bi‑GRU with tanh and an FC head; treating logs as depth sequences leverages vertical context.

- Feature selection: VP + GR + DEN + RES performed best among tested combinations. Resistivity was log‑transformed; all features were normalized to (−1, 1).

- Physics options: They tested the mudrock line; an empirical VP–VS relation fitted on the training well; and a multiparameter regression VS = a·GR + b·DEN + c·VP + d·RES + e. All three improved upon pure ML; pseudolabels + multiparam regression yielded the strongest gains in their blind‑well tests, but mudrock line still helped and is easy to implement first.

- Loss design: The adaptive min formulation improved stability and accuracy over fixed‑weight sums of losses in their tests.

---

This guideline gives you a clear path from theory to a maintainable, extensible codebase. Start with the mudrock line and the pseudolabel strategy to get immediate gains, then expand to richer rock‑physics models and alternate guidance strategies as your data and needs evolve.
