"""Base interface for rock-physics models."""
from __future__ import annotations

from abc import ABC, abstractmethod
from typing import Dict
import numpy as np


class RockPhysicsModel(ABC):
    """Abstract base class for rock-physics models.

    Every model must implement :meth:`estimate_vs`, which computes S-wave
    velocity given a dictionary of log curves. Optionally, models can
    implement :meth:`calibrate` to fit parameters from data.
    """

    name: str = "base"

    def calibrate(self, logs: Dict[str, np.ndarray], vs_true: np.ndarray) -> None:
        """Fit model parameters to training data.

        Parameters
        ----------
        logs:
            Mapping of log names to numpy arrays.
        vs_true:
            True S-wave velocity in km/s.
        """

    @abstractmethod
    def estimate_vs(self, logs: Dict[str, np.ndarray]) -> np.ndarray:
        """Estimate S-wave velocity from log curves.

        Parameters
        ----------
        logs:
            Mapping of log names to numpy arrays.

        Returns
        -------
        np.ndar<PERSON>
            Estimated S-wave velocity in km/s.
        """
        raise NotImplementedError
