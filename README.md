# Physics-Guided Bi-GRU Shear Sonic Prediction

This project implements a physics-guided machine learning workflow inspired by <PERSON> et al. (2024) for predicting shear sonic logs. A bidirectional GRU consumes cleaned well logs together with mudrock-line pseudolabels, producing trained models, QA plots, and text summaries suitable for reservoir studies.

## Quick Start

1. Install Python 3.10 or newer.
2. (Optional) Create and activate a virtual environment:
   ```
   python -m venv .venv
   .\.venv\Scripts\activate        # Windows
   source .venv/bin/activate       # macOS / Linux
   ```
3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
4. Place LAS files containing the required curves in the `Las/` directory.
5. Launch the end-to-end workflow:
   ```
   python bigru_training_with_outputs.py
   ```

## Input Expectations

| Curve           | Required? | Notes                                                                |
|-----------------|-----------|----------------------------------------------------------------------|
| `DEPTH`         | Optional  | Used to label outputs; indices are used if missing.                 |
| `P-WAVE` / `VP` | Required  | Converted to km/s if the LAS stores ft/s.                           |
| `S-WAVE` / `VS` | Optional  | When available, used as the supervised training label.              |
| `GR`            | Required  | Gamma ray log feature.                                              |
| `RHOB`          | Required  | Bulk density log feature.                                           |
| `RT`            | Required  | Resistivity log feature (log-transformed during preprocessing).     |

If more than one LAS file is present the script currently processes the first match. Update `list_las_files` in `src/utils/las_processing.py` to change this policy.

## What the Pipeline Produces

Each run creates a timestamped directory (e.g. `bigru_results_YYYYMMDD_HHMMSS/`) with:

- `bigru_model.pth` - trained BiGRU weights ready for reuse or fine-tuning.
- `training_config.json` - configuration snapshot including hyperparameters and feature list.
- `training_results.json` - scalar metrics such as correlation, RMSE, and MAE.
- `predictions_vs_actual.csv` - depth-aligned table with predictions, physics estimates, and residuals.
- `bigru_results_overview.png` - scatter, depth, and residual visualisations for quick QA.
- `error_analysis.png` - distribution comparison of BiGRU vs. physics residuals.
- `summary_report.txt` - human-readable report summarising data quality and model performance.

## Configuration Knobs

- `configs/default.yaml` controls the canonical feature set and target column expected by the trainer.
- `src/utils/las_processing.py` handles LAS parsing, masking bad samples, curve renaming, and unit conversion.
- `bigru_training_with_outputs.py` defines training length, patience, model size, plotting behaviour, and output layout. Adjust the `training_config` block in `run_bigru_training_with_outputs()` to tune hidden size, learning rate, or device targets.

GPU acceleration is selected automatically when `torch.cuda.is_available()` returns `True`.

## Repository Map

```
.
|-- bigru_training_with_outputs.py       # main orchestration script
|-- configs/
|   `-- default.yaml                     # default feature/target mapping
|-- src/
|   |-- data/                            # reusable data loaders
|   |-- models/                          # BiGRU network + custom losses
|   |-- pipelines/                       # training routines (pseudolabels, etc.)
|   |-- rock_physics/                    # mudrock line and future physics models
|   `-- utils/                           # LAS utilities and configuration helpers
|-- tests/                               # pytest suite for physics + utilities
|-- Las/                                 # place your LAS files here
|-- requirements.txt
`-- README.md
```

## Extending the Workflow

- Add new physics models by subclassing `RockPhysicsModel` in `src/rock_physics/` and wiring them into the pipelines.
- Implement alternate training strategies (physics-guided loss, transfer learning) by following the templates in `src/pipelines/` or the detailed guidance in `Guide_gpt.md`.
- Plug in additional QC plots or metrics by modifying `create_visualizations()` and the report writer in `bigru_training_with_outputs.py`.

## Testing

Run the existing unit tests and add more as you introduce new components:

```
pytest
```

## Further Reading

- Zhao, et al. (2024). *Physics-guided machine learning for shear sonic prediction*, Geophysics 89(1).
- `Guide_gpt.md` - in-repo design notes covering theory, architecture, and implementation tips for expanding the toolkit.
