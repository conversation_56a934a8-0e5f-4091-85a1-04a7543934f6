import os
import sys
from pathlib import Path
from datetime import datetime
import json

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import numpy as np
import torch
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

from src.models.networks import BiGRUNet
from src.pipelines.pseudolabel_training import train_with_pseudolabels
from src.rock_physics.mudrock import MudrockLine
from src.utils.las_processing import (
    load_las_dataframe, 
    prep_features_with_target, 
    list_las_files,
    convert_swave_units
)


def compute_regression_metrics(y_true, y_pred):
    """Compute regression metrics: MAE, RMSE, R², and Pearson correlation.

    Parameters
    ----------
    y_true : array-like
        Ground truth values
    y_pred : array-like
        Predicted values

    Returns
    -------
    dict
        Dictionary with keys: 'mae', 'rmse', 'r2', 'corr'
    """
    y_true = np.asarray(y_true, dtype=float)
    y_pred = np.asarray(y_pred, dtype=float)

    # Ensure same shape
    n = min(y_true.shape[0], y_pred.shape[0])
    y_true = y_true[:n]
    y_pred = y_pred[:n]

    with np.errstate(divide='ignore', invalid='ignore'):
        residuals = y_true - y_pred
        mae = float(np.nanmean(np.abs(residuals)))
        rmse = float(np.sqrt(np.nanmean(residuals ** 2)))

        sst = np.nansum((y_true - np.nanmean(y_true)) ** 2)
        sse = np.nansum((residuals) ** 2)
        r2 = float(1.0 - sse / sst) if sst > 0 else np.nan
        try:
            corr = float(np.corrcoef(y_true, y_pred)[0, 1]) if n > 1 else np.nan
        except Exception:
            corr = np.nan

    return {"mae": mae, "rmse": rmse, "r2": r2, "corr": corr}


def save_training_results(output_dir, model, training_config, results):
    """Save model and training configuration."""
    # Save model
    model_path = output_dir / "bigru_model.pth"
    torch.save(model.state_dict(), model_path)
    print(f"Model saved to: {model_path}")
    
    # Save training config
    config_path = output_dir / "training_config.json"
    with open(config_path, 'w') as f:
        json.dump(training_config, f, indent=2)
    print(f"Training config saved to: {config_path}")
    
    # Save results summary
    results_path = output_dir / "training_results.json"
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2)
    print(f"Training results saved to: {results_path}")


def save_predictions(output_dir, predictions, targets, physics_estimates, depth_masked, feature_names):
    """Save predictions and create comparison CSV."""
    # Create comprehensive results DataFrame
    results_df = pd.DataFrame({
        'depth': depth_masked,
        'vs_actual': targets,
        'vs_predicted': predictions,
        'vs_physics': physics_estimates,
        'residual': targets - predictions,
        'physics_residual': targets - physics_estimates,
    })
    
    # Add some derived metrics
    results_df['abs_error'] = np.abs(results_df['residual'])
    results_df['rel_error_pct'] = (results_df['residual'] / results_df['vs_actual']) * 100
    results_df['physics_abs_error'] = np.abs(results_df['physics_residual'])
    results_df['physics_rel_error_pct'] = (results_df['physics_residual'] / results_df['vs_actual']) * 100
    
    # Save to CSV
    csv_path = output_dir / "predictions_vs_actual.csv"
    results_df.to_csv(csv_path, index=False)
    print(f"Predictions saved to: {csv_path}")
    
    return results_df


def save_test_predictions(output_dir, well_name, predictions, physics_estimates, depth_masked, targets=None):
    """Save predictions for a test well. If targets are provided, include error columns.

    Parameters
    ----------
    output_dir : Path
        Directory to write outputs
    well_name : str
        Well identifier used in filename
    predictions : np.ndarray
        Predicted VS (km/s)
    physics_estimates : np.ndarray
        Physics VS estimates (km/s)
    depth_masked : np.ndarray
        Depth values aligned to predictions
    targets : Optional[np.ndarray]
        Actual VS if available
    """
    data = {
        'depth': depth_masked,
        'vs_predicted': predictions,
        'vs_physics': physics_estimates,
    }
    if targets is not None:
        data['vs_actual'] = targets
        data['residual'] = targets - predictions
        data['physics_residual'] = targets - physics_estimates
        data['abs_error'] = np.abs(data['residual'])
        # Avoid division by zero in relative error
        with np.errstate(divide='ignore', invalid='ignore'):
            data['rel_error_pct'] = (data['residual'] / targets) * 100
            data['physics_rel_error_pct'] = (data['physics_residual'] / targets) * 100

    df = pd.DataFrame(data)
    csv_path = output_dir / f"predictions_{well_name}.csv"
    df.to_csv(csv_path, index=False)
    print(f"Test predictions saved to: {csv_path}")
    return df


def create_test_visualizations(output_dir, well_name, predictions, physics_estimates, depth_masked, targets=None):
    """Create and save prediction plots for a test well."""
    plt.style.use('default')
    if targets is not None:
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        # Predicted vs Actual
        ax1.scatter(targets, predictions, alpha=0.6, s=1)
        lo = min(float(np.nanmin(targets)), float(np.nanmin(predictions)))
        hi = max(float(np.nanmax(targets)), float(np.nanmax(predictions)))
        ax1.plot([lo, hi], [lo, hi], 'r--', lw=2)
        # Metrics for BiGRU vs Actual
        m_pred = compute_regression_metrics(targets, predictions)
        corr = m_pred["corr"]
        ax1.set_xlabel('Actual S-wave (km/s)')
        ax1.set_ylabel('Predicted S-wave (km/s)')
        ax1.set_title(f'Predictions vs Actual — {well_name}\nCorrelation: {corr:.3f}')
        # Annotate metrics
        ax1.text(
            0.05,
            0.95,
            f"R²: {m_pred['r2']:.3f}\nMAE: {m_pred['mae']:.3f} km/s\nRMSE: {m_pred['rmse']:.3f} km/s",
            transform=ax1.transAxes,
            ha='left', va='top',
            fontsize=10,
            bbox=dict(facecolor='white', alpha=0.7, edgecolor='none')
        )
        ax1.grid(True, alpha=0.3)

        # Physics vs Actual
        ax2.scatter(targets, physics_estimates, alpha=0.6, s=1, color='orange')
        lo2 = min(float(np.nanmin(targets)), float(np.nanmin(physics_estimates)))
        hi2 = max(float(np.nanmax(targets)), float(np.nanmax(physics_estimates)))
        ax2.plot([lo2, hi2], [lo2, hi2], 'r--', lw=2)
        # Metrics for Physics vs Actual
        m_phys = compute_regression_metrics(targets, physics_estimates)
        corrp = m_phys["corr"]
        ax2.set_xlabel('Actual S-wave (km/s)')
        ax2.set_ylabel('Physics S-wave (km/s)')
        ax2.set_title(f'Physics vs Actual — {well_name}\nCorrelation: {corrp:.3f}')
        # Annotate metrics
        ax2.text(
            0.05,
            0.95,
            f"R²: {m_phys['r2']:.3f}\nMAE: {m_phys['mae']:.3f} km/s\nRMSE: {m_phys['rmse']:.3f} km/s",
            transform=ax2.transAxes,
            ha='left', va='top',
            fontsize=10,
            bbox=dict(facecolor='white', alpha=0.7, edgecolor='none')
        )
        ax2.grid(True, alpha=0.3)

        # Depth curves
        ax3.plot(targets, depth_masked, label='Actual', linewidth=1)
        ax3.plot(predictions, depth_masked, label='Predicted', linewidth=1)
        ax3.plot(physics_estimates, depth_masked, label='Physics', linewidth=1, alpha=0.7)
        ax3.set_xlabel('S-wave velocity (km/s)')
        ax3.set_ylabel('Depth (m)')
        ax3.set_title(f'S-wave Velocities vs Depth — {well_name}')
        # Annotate aggregated metrics for both models
        ax3.text(
            0.98,
            0.02,
            f"BiGRU  - MAE: {m_pred['mae']:.3f}, RMSE: {m_pred['rmse']:.3f}\n"
            f"Physics - MAE: {m_phys['mae']:.3f}, RMSE: {m_phys['rmse']:.3f} (km/s)",
            transform=ax3.transAxes,
            ha='right', va='bottom',
            fontsize=9,
            bbox=dict(facecolor='white', alpha=0.7, edgecolor='none')
        )
        ax3.legend(); ax3.grid(True, alpha=0.3); ax3.invert_yaxis()

        # Residuals
        resid = targets - predictions
        pres = targets - physics_estimates
        ax4.hist(resid, bins=50, alpha=0.7, label='BiGRU Residuals')
        ax4.hist(pres, bins=50, alpha=0.7, label='Physics Residuals')
        ax4.set_xlabel('Residual (km/s)')
        ax4.set_ylabel('Frequency')
        ax4.set_title(f'Residuals — {well_name}')
        # Annotate residual metrics
        ax4.text(
            0.05,
            0.95,
            f"BiGRU  - MAE: {m_pred['mae']:.3f}, RMSE: {m_pred['rmse']:.3f}\n"
            f"Physics - MAE: {m_phys['mae']:.3f}, RMSE: {m_phys['rmse']:.3f} (km/s)",
            transform=ax4.transAxes,
            ha='left', va='top',
            fontsize=10,
            bbox=dict(facecolor='white', alpha=0.7, edgecolor='none')
        )
        ax4.legend(); ax4.grid(True, alpha=0.3)
        plt.tight_layout()
    else:
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        # Depth curves (no actuals)
        ax1.plot(predictions, depth_masked, label='Predicted', linewidth=1)
        ax1.plot(physics_estimates, depth_masked, label='Physics', linewidth=1, alpha=0.7)
        ax1.set_xlabel('S-wave velocity (km/s)')
        ax1.set_ylabel('Depth (m)')
        ax1.set_title(f'S-wave Velocities vs Depth — {well_name}')
        # Informative note for missing targets
        ax1.text(
            0.98,
            0.02,
            "No actual target available — error metrics not computed",
            transform=ax1.transAxes,
            ha='right', va='bottom',
            fontsize=9,
            bbox=dict(facecolor='white', alpha=0.7, edgecolor='none')
        )
        ax1.legend(); ax1.grid(True, alpha=0.3); ax1.invert_yaxis()

        # Predicted vs Physics
        ax2.scatter(physics_estimates, predictions, alpha=0.6, s=1, color='green')
        lo = min(float(np.nanmin(physics_estimates)), float(np.nanmin(predictions)))
        hi = max(float(np.nanmax(physics_estimates)), float(np.nanmax(predictions)))
        ax2.plot([lo, hi], [lo, hi], 'r--', lw=2)
        ax2.set_xlabel('Physics S-wave (km/s)')
        ax2.set_ylabel('Predicted S-wave (km/s)')
        ax2.set_title(f'Predicted vs Physics — {well_name}')
        ax2.grid(True, alpha=0.3)
        plt.tight_layout()

    plot_path = output_dir / f"predictions_overview_{well_name}.png"
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"Prediction overview plot saved to: {plot_path}")


def create_visualizations(output_dir, results_df, correlation, feature_names, well_name: str | None = None):
    """Create and save visualization plots for the training well.

    Parameters
    ----------
    output_dir : Path
        Output directory
    results_df : pd.DataFrame
        DataFrame from save_predictions with columns depth, vs_actual, vs_predicted, vs_physics
    correlation : float
        Correlation between predictions and actuals
    feature_names : list[str]
        Names of features used (informational)
    well_name : Optional[str]
        Well name to include in plot titles
    """
    plt.style.use('default')
    
    # 1. Predictions vs Actual scatter plot
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # Scatter plot: Predictions vs Actual
    ax1.scatter(results_df['vs_actual'], results_df['vs_predicted'], alpha=0.6, s=1)
    ax1.plot([results_df['vs_actual'].min(), results_df['vs_actual'].max()], 
             [results_df['vs_actual'].min(), results_df['vs_actual'].max()], 'r--', lw=2)
    ax1.set_xlabel('Actual S-wave (km/s)')
    ax1.set_ylabel('Predicted S-wave (km/s)')
    title_suffix = f" — {well_name}" if well_name else ""
    # Metrics for BiGRU vs Actual
    m_pred = compute_regression_metrics(results_df['vs_actual'], results_df['vs_predicted'])
    ax1.set_title(f'BiGRU Predictions vs Actual{title_suffix}\nCorrelation: {correlation:.3f}')
    ax1.text(
        0.05,
        0.95,
        f"R²: {m_pred['r2']:.3f}\nMAE: {m_pred['mae']:.3f} km/s\nRMSE: {m_pred['rmse']:.3f} km/s",
        transform=ax1.transAxes,
        ha='left', va='top',
        fontsize=10,
        bbox=dict(facecolor='white', alpha=0.7, edgecolor='none')
    )
    ax1.grid(True, alpha=0.3)
    
    # Physics vs Actual scatter plot
    m_phys = compute_regression_metrics(results_df['vs_actual'], results_df['vs_physics'])
    physics_corr = m_phys["corr"]
    ax2.scatter(results_df['vs_actual'], results_df['vs_physics'], alpha=0.6, s=1, color='orange')
    ax2.plot([results_df['vs_actual'].min(), results_df['vs_actual'].max()], 
             [results_df['vs_actual'].min(), results_df['vs_actual'].max()], 'r--', lw=2)
    ax2.set_xlabel('Actual S-wave (km/s)')
    ax2.set_ylabel('Physics S-wave (km/s)')
    ax2.set_title(f'Physics Estimates vs Actual{title_suffix}\nCorrelation: {physics_corr:.3f}')
    ax2.text(
        0.05,
        0.95,
        f"R²: {m_phys['r2']:.3f}\nMAE: {m_phys['mae']:.3f} km/s\nRMSE: {m_phys['rmse']:.3f} km/s",
        transform=ax2.transAxes,
        ha='left', va='top',
        fontsize=10,
        bbox=dict(facecolor='white', alpha=0.7, edgecolor='none')
    )
    ax2.grid(True, alpha=0.3)
    
    # Depth plot: All curves
    ax3.plot(results_df['vs_actual'], results_df['depth'], label='Actual', linewidth=1)
    ax3.plot(results_df['vs_predicted'], results_df['depth'], label='Predicted', linewidth=1)
    ax3.plot(results_df['vs_physics'], results_df['depth'], label='Physics', linewidth=1, alpha=0.7)
    ax3.set_xlabel('S-wave velocity (km/s)')
    ax3.set_ylabel('Depth (m)')
    ax3.set_title(f'S-wave Velocities vs Depth{title_suffix}')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    # Annotate aggregated metrics for both models on the depth plot
    ax3.text(
        0.98,
        0.02,
        f"BiGRU  - MAE: {m_pred['mae']:.3f}, RMSE: {m_pred['rmse']:.3f}\n"
        f"Physics - MAE: {m_phys['mae']:.3f}, RMSE: {m_phys['rmse']:.3f} (km/s)",
        transform=ax3.transAxes,
        ha='right', va='bottom',
        fontsize=9,
        bbox=dict(facecolor='white', alpha=0.7, edgecolor='none')
    )
    ax3.invert_yaxis()
    
    # Residuals plot
    ax4.hist(results_df['residual'], bins=50, alpha=0.7, label='BiGRU Residuals')
    ax4.hist(results_df['physics_residual'], bins=50, alpha=0.7, label='Physics Residuals')
    ax4.set_xlabel('Residual (km/s)')
    ax4.set_ylabel('Frequency')
    ax4.set_title(f'Residuals Distribution{title_suffix}')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    # Annotate residual metrics on histogram plot
    ax4.text(
        0.05,
        0.95,
        f"BiGRU  - MAE: {m_pred['mae']:.3f}, RMSE: {m_pred['rmse']:.3f}\n"
        f"Physics - MAE: {m_phys['mae']:.3f}, RMSE: {m_phys['rmse']:.3f} (km/s)",
        transform=ax4.transAxes,
        ha='left', va='top',
        fontsize=10,
        bbox=dict(facecolor='white', alpha=0.7, edgecolor='none')
    )
    
    plt.tight_layout()
    well_stem = Path(well_name).stem if well_name else None
    plot_filename = f"bigru_results_overview_{well_stem}.png" if well_stem else "bigru_results_overview.png"
    plot_path = output_dir / plot_filename
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"Overview plot saved to: {plot_path}")
    
    # 2. Error analysis plot
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # Absolute errors
    ax1.boxplot([results_df['abs_error'], results_df['physics_abs_error']], 
                tick_labels=['BiGRU', 'Physics'])
    ax1.set_ylabel('Absolute Error (km/s)')
    ax1.set_title('Absolute Error Comparison')
    ax1.grid(True, alpha=0.3)
    # Annotate with summary stats
    abs_mae_bigru = float(np.nanmean(results_df['abs_error']))
    abs_med_bigru = float(np.nanmedian(results_df['abs_error']))
    abs_mae_phys = float(np.nanmean(results_df['physics_abs_error']))
    abs_med_phys = float(np.nanmedian(results_df['physics_abs_error']))
    ax1.text(
        0.02,
        0.98,
        f"BiGRU  — mean: {abs_mae_bigru:.3f}, median: {abs_med_bigru:.3f}\n"
        f"Physics — mean: {abs_mae_phys:.3f}, median: {abs_med_phys:.3f} (km/s)",
        transform=ax1.transAxes,
        ha='left', va='top',
        fontsize=9,
        bbox=dict(facecolor='white', alpha=0.7, edgecolor='none')
    )
    
    # Relative errors
    ax2.boxplot([results_df['rel_error_pct'], results_df['physics_rel_error_pct']], 
                tick_labels=['BiGRU', 'Physics'])
    ax2.set_ylabel('Relative Error (%)')
    ax2.set_title('Relative Error Comparison')
    ax2.grid(True, alpha=0.3)
    # Annotate with summary stats
    rel_mean_bigru = float(np.nanmean(results_df['rel_error_pct']))
    rel_med_bigru = float(np.nanmedian(results_df['rel_error_pct']))
    rel_mean_phys = float(np.nanmean(results_df['physics_rel_error_pct']))
    rel_med_phys = float(np.nanmedian(results_df['physics_rel_error_pct']))
    ax2.text(
        0.02,
        0.98,
        f"BiGRU  — mean: {rel_mean_bigru:.2f}%, median: {rel_med_bigru:.2f}%\n"
        f"Physics — mean: {rel_mean_phys:.2f}%, median: {rel_med_phys:.2f}%",
        transform=ax2.transAxes,
        ha='left', va='top',
        fontsize=9,
        bbox=dict(facecolor='white', alpha=0.7, edgecolor='none')
    )
    
    plt.tight_layout()
    error_plot_filename = f"error_analysis_{well_stem}.png" if well_stem else "error_analysis.png"
    error_plot_path = output_dir / error_plot_filename
    plt.savefig(error_plot_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"Error analysis plot saved to: {error_plot_path}")


def create_summary_report(output_dir, results_df, correlation, training_config, results):
    """Create a text summary report."""
    report_path = output_dir / "summary_report.txt"
    
    with open(report_path, 'w') as f:
        f.write("=" * 80 + "\n")
        f.write("BiGRU S-WAVE PREDICTION RESULTS SUMMARY\n")
        f.write("=" * 80 + "\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("TRAINING CONFIGURATION:\n")
        f.write("-" * 40 + "\n")
        for key, value in training_config.items():
            f.write(f"{key}: {value}\n")
        f.write("\n")
        
        f.write("DATA STATISTICS:\n")
        f.write("-" * 40 + "\n")
        f.write(f"Total data points: {len(results_df)}\n")
        f.write(f"Depth range: {results_df['depth'].min():.1f} - {results_df['depth'].max():.1f} m\n")
        f.write(f"S-wave range (actual): {results_df['vs_actual'].min():.3f} - {results_df['vs_actual'].max():.3f} km/s\n")
        f.write(f"S-wave range (predicted): {results_df['vs_predicted'].min():.3f} - {results_df['vs_predicted'].max():.3f} km/s\n\n")
        
        f.write("PERFORMANCE METRICS:\n")
        f.write("-" * 40 + "\n")
        f.write(f"Correlation (BiGRU): {correlation:.4f}\n")
        physics_corr = np.corrcoef(results_df['vs_actual'], results_df['vs_physics'])[0, 1]
        f.write(f"Correlation (Physics): {physics_corr:.4f}\n")
        f.write(f"RMSE (BiGRU): {np.sqrt(np.mean(results_df['residual']**2)):.4f} km/s\n")
        f.write(f"RMSE (Physics): {np.sqrt(np.mean(results_df['physics_residual']**2)):.4f} km/s\n")
        f.write(f"MAE (BiGRU): {np.mean(results_df['abs_error']):.4f} km/s\n")
        f.write(f"MAE (Physics): {np.mean(results_df['physics_abs_error']):.4f} km/s\n")
        f.write(f"Mean relative error (BiGRU): {np.mean(results_df['rel_error_pct']):.2f}%\n")
        f.write(f"Mean relative error (Physics): {np.mean(results_df['physics_rel_error_pct']):.2f}%\n\n")
        
        f.write("STATISTICAL SUMMARY:\n")
        f.write("-" * 40 + "\n")
        f.write("Actual S-wave statistics:\n")
        f.write(f"  Mean: {results_df['vs_actual'].mean():.4f} km/s\n")
        f.write(f"  Std:  {results_df['vs_actual'].std():.4f} km/s\n")
        f.write("Predicted S-wave statistics:\n")
        f.write(f"  Mean: {results_df['vs_predicted'].mean():.4f} km/s\n")
        f.write(f"  Std:  {results_df['vs_predicted'].std():.4f} km/s\n")
        f.write("Physics S-wave statistics:\n")
        f.write(f"  Mean: {results_df['vs_physics'].mean():.4f} km/s\n")
        f.write(f"  Std:  {results_df['vs_physics'].std():.4f} km/s\n")
    
    print(f"Summary report saved to: {report_path}")


def run_bigru_training_with_outputs():
    """Run BiGRU training and save all outputs."""
    print("=" * 80)
    print("BiGRU S-WAVE PREDICTION WITH OUTPUT SAVING")
    print("=" * 80)
    
    # Create output directory with timestamp inside 'output' folder
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_root = Path("output")
    output_root.mkdir(exist_ok=True)
    output_dir = output_root / Path(f"bigru_results_{timestamp}")
    output_dir.mkdir(exist_ok=True)
    print(f"Output directory: {output_dir.absolute()}")
    
    # Point to provided LAS directory
    las_dir = Path(r"Las")
    if not las_dir.exists():
        las_dir = Path(r"c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\Bigru\Bigru_GPT\Las")
    
    # Find LAS files (for display) and set specific train/test wells
    las_files = list_las_files(las_dir)
    if not las_files:
        print(f"ERROR: No LAS files found in {las_dir}")
        return False

    # Define train wells and compute test wells dynamically (train on two, predict on the rest)
    train_well_names = ["B-L-15_RP_INPUT.las", "B-G-6_RP_INPUT.las"]
    test_well_names = [p.name for p in las_files if p.name not in set(train_well_names)]
    training_plot_stem = "__".join([Path(n).stem for n in train_well_names])

    print("\nTraining on wells:")
    for tn in train_well_names:
        print(f"  - {tn}")

    # Accumulate training data across the selected wells (concatenate along depth)
    Xn_list, y_list, vs_phys_list, depth_masked_list = [], [], [], []
    feat_names = None
    phys = MudrockLine()

    for train_name in train_well_names:
        train_path = las_dir / train_name
        if not train_path.exists():
            print(f"ERROR: Training well not found: {train_path}")
            return False
        curves = load_las_dataframe(train_path)

        y_all_i = curves.get("vs")
        if y_all_i is None:
            print(f"ERROR: Training well has no S-WAVE (vs): {train_path}")
            return False
        y_all_i = np.asarray(y_all_i, dtype=float)
        y_all_i = convert_swave_units(y_all_i)

        Xn_i, feat_names_i, mask_i = prep_features_with_target(curves, y_all_i)
        if feat_names is None:
            feat_names = feat_names_i

        vp_i = np.asarray(curves["vp"], dtype=float)[mask_i]
        vs_phys_i = phys.estimate_vs({"vp": vp_i}).astype(np.float32)

        y_i = y_all_i[mask_i].astype(np.float32)

        depth_all_i = curves.get("depth")
        if depth_all_i is not None:
            depth_masked_i = np.asarray(depth_all_i, dtype=float)[mask_i]
        else:
            depth_masked_i = np.arange(len(y_i))

        Xn_list.append(Xn_i)
        y_list.append(y_i)
        vs_phys_list.append(vs_phys_i)
        depth_masked_list.append(depth_masked_i)

    # Concatenate data across training wells
    Xn = np.vstack(Xn_list)
    y = np.concatenate(y_list)
    vs_phys = np.concatenate(vs_phys_list)
    depth_masked = np.concatenate(depth_masked_list)
    print(f"Features used: {feat_names}")
    print(f"Data points after cleaning (all training wells): {len(Xn)}")
    
    # Training configuration
    training_config = {
        "model_type": "BiGRUNet",
        "input_dim": Xn.shape[1] + 1,
        "hidden_dim": 16,
        "num_layers": 1,
        "use_tanh": True,
        "learning_rate": 1e-3,
        "epochs": 15,
        "patience": 5,
        "device": "cuda" if torch.cuda.is_available() else "cpu",
        "features": feat_names,
        "data_points": len(Xn),
        "train_wells": train_well_names,
    }
    
    # Create and train model
    model = BiGRUNet(in_dim=training_config["input_dim"], 
                     hidden_dim=training_config["hidden_dim"], 
                     num_layers=training_config["num_layers"], 
                     use_tanh=training_config["use_tanh"])
    
    print(f"Training BiGRU model on {training_config['device']}...")
    model = train_with_pseudolabels(
        model, Xn, y, vs_phys, 
        lr=training_config["learning_rate"], 
        epochs=training_config["epochs"], 
        patience=training_config["patience"], 
        device=training_config["device"]
    )
    
    # Make predictions
    print("Making predictions...")
    with torch.no_grad():
        X_aug = np.hstack([Xn, vs_phys[:, None]])
        xb = torch.from_numpy(X_aug).float().unsqueeze(0).to(training_config["device"])
        preds = model(xb).detach().cpu().numpy().ravel()
    
    # Calculate correlation
    correlation = np.corrcoef(preds, y)[0, 1]
    
    # Prepare results
    results = {
        "correlation": float(correlation),
        "rmse": float(np.sqrt(np.mean((preds - y)**2))),
        "mae": float(np.mean(np.abs(preds - y))),
        "prediction_stats": {
            "mean": float(preds.mean()),
            "std": float(preds.std()),
            "min": float(preds.min()),
            "max": float(preds.max()),
        },
        "target_stats": {
            "mean": float(y.mean()),
            "std": float(y.std()),
            "min": float(y.min()),
            "max": float(y.max()),
        },
    }
    
    print(f"\nTraining completed successfully!")
    print(f"Correlation: {correlation:.4f}")
    print(f"RMSE: {results['rmse']:.4f} km/s")
    print(f"MAE: {results['mae']:.4f} km/s")
    
    # Save all outputs for training well
    print(f"\nSaving results to {output_dir}...")
    
    save_training_results(output_dir, model, training_config, results)
    results_df = save_predictions(output_dir, preds, y, vs_phys, depth_masked, feat_names)
    create_visualizations(output_dir, results_df, correlation, feat_names, well_name=training_plot_stem)
    create_summary_report(output_dir, results_df, correlation, training_config, results)

    # ------------------------------------------------------------------
    # Inference on other wells
    # ------------------------------------------------------------------
    print("\nRunning predictions on other wells...")
    for test_name in test_well_names:
        test_path = las_dir / test_name
        if not test_path.exists():
            print(f"WARNING: Test well not found, skipping: {test_path}")
            continue

        print(f"\nPredicting on: {test_path.name}")
        curves_t = load_las_dataframe(test_path)

        # Prepare optional target if present
        y_all_t = None
        if curves_t["vs"] is not None:
            y_all_t = np.asarray(curves_t["vs"], dtype=float)
            y_all_t = convert_swave_units(y_all_t)

        # Prepare features; include target if available to align masks
        Xn_t, feat_names_t, mask_t = prep_features_with_target(curves_t, y_all_t)

        # Physics estimates for test well
        vp_t = np.asarray(curves_t["vp"], dtype=float)[mask_t]
        vs_phys_t = phys.estimate_vs({"vp": vp_t}).astype(np.float32)

        # Depth for output
        depth_all_t = curves_t.get("depth")
        if depth_all_t is not None:
            depth_masked_t = np.asarray(depth_all_t, dtype=float)[mask_t]
        else:
            depth_masked_t = np.arange(len(Xn_t))

        # Predict
        with torch.no_grad():
            X_aug_t = np.hstack([Xn_t, vs_phys_t[:, None]])
            xb_t = torch.from_numpy(X_aug_t).float().unsqueeze(0).to(training_config["device"])
            preds_t = model(xb_t).detach().cpu().numpy().ravel()

        # If we have targets, compute quick metrics
        if y_all_t is not None:
            y_t = y_all_t[mask_t].astype(np.float32)
            corr_t = float(np.corrcoef(preds_t, y_t)[0, 1])
            rmse_t = float(np.sqrt(np.mean((preds_t - y_t) ** 2)))
            mae_t = float(np.mean(np.abs(preds_t - y_t)))
            print(f"Metrics [{test_path.name}]: Corr={corr_t:.4f} | RMSE={rmse_t:.4f} km/s | MAE={mae_t:.4f} km/s")
            save_test_predictions(output_dir, Path(test_name).stem, preds_t, vs_phys_t, depth_masked_t, targets=y_t)
            create_test_visualizations(output_dir, Path(test_name).stem, preds_t, vs_phys_t, depth_masked_t, targets=y_t)
        else:
            save_test_predictions(output_dir, Path(test_name).stem, preds_t, vs_phys_t, depth_masked_t, targets=None)
            create_test_visualizations(output_dir, Path(test_name).stem, preds_t, vs_phys_t, depth_masked_t, targets=None)
    
    print("\n" + "=" * 80)
    print("ALL RESULTS SAVED SUCCESSFULLY!")
    print("=" * 80)
    print(f"Output directory: {output_dir.absolute()}")
    print("Files generated:")
    print("  - bigru_model.pth (trained model)")
    print("  - training_config.json (model configuration)")
    print("  - training_results.json (performance metrics)")
    print("  - predictions_vs_actual.csv (detailed results for training well)")
    well_stem_print = training_plot_stem
    print(f"  - bigru_results_overview_{well_stem_print}.png (training plots)")
    print(f"  - error_analysis_{well_stem_print}.png (training error comparison)")
    print("  - summary_report.txt (comprehensive summary)")
    
    return True


if __name__ == "__main__":
    try:
        success = run_bigru_training_with_outputs()
        if not success:
            sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)