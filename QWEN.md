# Project Context for Qwen Code

This document provides essential context about the "Physics-Guided Bi-GRU Shear Sonic Prediction" project for Qwen Code to use in future interactions.

## Project Overview

This project implements a physics-guided machine learning workflow, inspired by <PERSON> et al. (2024), to predict shear sonic (S-wave) logs from conventional well log data. It uses a Bidirectional Gated Recurrent Unit (Bi-GRU) neural network. The model is trained using cleaned well logs (Gamma Ray, Bulk Density, Resistivity, P-wave velocity) combined with pseudolabels generated by a simple rock physics model (the Mudrock line). The pipeline produces trained models, quality assurance (QA) plots, and text summaries suitable for reservoir studies.

## Core Technologies

- **Language:** Python 3.10+
- **ML Framework:** PyTorch
- **Data Processing:** NumPy, Pandas
- **File Format:** LAS (Well Log ASCII Standard) via `lasio`
- **Configuration:** YAML
- **Testing:** Pytest

## Project Structure

```
.
|-- bigru_training_with_outputs.py       # Main orchestration script
|-- configs/
|   `-- default.yaml                     # Default feature/target mapping
|-- src/
|   |-- data/                            # (Currently minimal, processing in utils)
|   |-- models/                          # BiGRU network definition
|   |-- pipelines/                       # Training routines (pseudolabels)
|   |-- rock_physics/                    # Rock physics models (e.g., Mudrock line)
|   `-- utils/                           # LAS utilities and helpers
|-- tests/                               # Pytest suite
|-- Las/                                 # Input LAS files
|-- output/                              # Generated results (timestamped folders)
|-- requirements.txt
|-- README.md
`-- Guide_gpt.md                         # In-depth design and implementation guide
```

## Key Components

1.  **Data Ingestion (`src/utils/las_processing.py`):**
    *   Reads LAS files using `lasio`.
    *   Identifies required curves (`P-WAVE`/`VP`, `GR`, `RHOB`/`DEN`, `RT`/`RES`, optional `S-WAVE`/`VS`, optional `DEPTH`).
    *   Converts units (e.g., P-wave and S-wave velocities to km/s if stored in m/s).
    *   Applies preprocessing: log-transforms resistivity (`log(RT)`) and normalizes features to the range [-1, 1].
    *   Handles missing data by masking non-finite values across all features and the target.
2.  **Rock Physics Model (`src/rock_physics/`):**
    *   `base.py`: Defines the abstract `RockPhysicsModel` interface.
    *   `mudrock.py`: Implements the `MudrockLine` model (`VS = (VP - 1.36) / 1.16`).
3.  **Neural Network (`src/models/networks.py`):**
    *   `BiGRUNet`: A simple Bi-GRU network with a linear output layer.
4.  **Training Pipeline (`src/pipelines/pseudolabel_training.py`):**
    *   `train_with_pseudolabels`: Trains the `BiGRUNet` using the physics-guided pseudolabel strategy. The pseudolabel (e.g., Mudrock line estimate) is appended as an additional feature to the input data.
    *   Uses MSE loss, Adam optimizer, and early stopping.
5.  **Main Script (`bigru_training_with_outputs.py`):**
    *   Orchestrates the entire workflow.
    *   Loads data from LAS files (currently hardcodes specific training wells).
    *   Prepares features and targets.
    *   Generates pseudolabels using the `MudrockLine` model.
    *   Configures and trains the `BiGRUNet` model.
    *   Generates predictions.
    *   Calculates performance metrics (MAE, RMSE, R², Correlation).
    *   Saves the trained model (`bigru_model.pth`), configuration (`training_config.json`), and results (`training_results.json`).
    *   Creates detailed CSV outputs (`predictions_vs_actual.csv`).
    *   Generates visualization plots (`bigru_results_overview.png`, `error_analysis.png`).
    *   Writes a summary report (`summary_report.txt`).
    *   Runs inference on other LAS files in the `Las/` directory.

## Building, Running, and Testing

1.  **Setup:**
    *   Ensure Python 3.10+ is installed.
    *   (Optional but recommended) Create and activate a virtual environment.
    *   Install dependencies: `pip install -r requirements.txt`
2.  **Running:**
    *   Place LAS files in the `Las/` directory.
    *   Execute the main script: `python bigru_training_with_outputs.py`
    *   Outputs will be saved in a new timestamped directory within `output/`.
3.  **Testing:**
    *   Run the test suite: `pytest`

## Development Conventions

*   **Modularity:** Code is organized into modules by function (data, models, pipelines, rock physics).
*   **Interfaces:** Abstract base classes (e.g., `RockPhysicsModel`) are used to define interfaces for extensibility (e.g., adding new physics models like Xu-White).
*   **Configuration:** Model and training parameters are defined in `configs/default.yaml` and within the main script.
*   **Data Flow:** Data processing and preparation are handled in `src/utils/las_processing.py`.
*   **Output:** The main script handles saving models, results, plots, and reports in a structured manner.
*   **Physics Guidance:** The primary physics guidance strategy implemented is "pseudolabels". Other strategies (physics-guided loss, transfer learning) are discussed in `Guide_gpt.md`.

## Key Files for Context

*   `README.md`: Primary user documentation.
*   `Guide_gpt.md`: Detailed design notes, theoretical background, and implementation guidance.
*   `bigru_training_with_outputs.py`: Central script for execution and output generation.
*   `requirements.txt`: Python dependencies.
*   `configs/default.yaml`: Defines the default feature set and target.
*   `src/utils/las_processing.py`: Core data loading and preprocessing logic.
*   `src/rock_physics/mudrock.py`: Implementation of the mudrock line physics model.
*   `src/models/networks.py`: Definition of the Bi-GRU neural network.
*   `src/pipelines/pseudolabel_training.py`: Implementation of the training pipeline.